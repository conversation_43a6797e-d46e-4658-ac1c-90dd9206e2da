import { supabase } from '../lib/supabase';
import { Workout, ExerciseSession, WorkoutSet, MonthlyAnalytics } from '../types';
import type { DatabaseExerciseSession } from '../lib/supabase';

export const workoutService = {
  // Save a complete workout
  async saveWorkout(workout: Omit<Workout, 'id'>): Promise<Workout> {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      throw new Error('User must be authenticated to save workouts');
    }

    // Start a transaction by creating the workout first
    const { data: workoutData, error: workoutError } = await supabase
      .from('workouts')
      .insert({
        user_id: user.id,
        date: workout.date,
        start_time: workout.startTime,
        end_time: workout.endTime,
        muscle_group: workout.muscleGroup,
        total_sets: workout.totalSets,
        total_reps: workout.totalReps
      })
      .select()
      .single();

    if (workoutError) {
      console.error('Error creating workout:', workoutError);
      throw workoutError;
    }

    // Save exercise sessions
    const exerciseSessionPromises = workout.exercises.map(async (
        exercise) => {
      const { data: sessionData } = await supabase
        .from('exercise_sessions')
        .insert({
          workout_id: workoutData.id,
          exercise_id: exercise.exerciseId,
          exercise_name: exercise.exerciseName,
          start_time: exercise.startTime,
          end_time: exercise.endTime,
          muscle_group: exercise.muscleGroup,
          max_weight: exercise.maxWeight
        })
        .select()
        .single();

      // Save workout sets for this exercise session
      const setPromis = exercise.sets.map(async (set, setIndex) => {
        const { data: setData, error: setError } = await supabase
          .from('workout_sets')
          .insert({
            exercise_session_id: sessionData.id,
            reps: set.reps,
            weight: set.weight,
            set_order: setIndex + 1
          })
          .select()
          .single();

        if (setError) {
          console.error('Error creating workout set:', setError);
          throw setError;
        }

        return setData;
      });

      const sets = await Promise.all(setPromis);
      
      return {
        ...this.mapDatabaseToExerciseSession(sessionData),
        sets: sets.map(this.mapDatabaseToWorkoutSet)
      };
    });

    const exercises = await Promise.all(exerciseSessionPromises);

    return {
      id: workoutData.id,
      date: workoutData.date,
      startTime: workoutData.start_time,
      endTime: workoutData.end_time || '',
      muscleGroup: workoutData.muscle_group,
      totalSets: workoutData.total_sets,
      totalReps: workoutData.total_reps,
      exercises
    };
  },

  // Get all workouts for the current user
  async getWorkouts(): Promise<Workout[]> {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      return [];
    }

    const { data: workouts, error: workoutsError } = await supabase
      .from('workouts')
      .select(`
        *,
        exercise_sessions (
          *,
          workout_sets (*)
        )
      `)
      .eq('user_id', user.id)
      .order('date', { ascending: false })
      .order('start_time', { ascending: false });

    if (workoutsError) {
      console.error('Error fetching workouts:', workoutsError);
      throw workoutsError;
    }

    return workouts.map((workout : Workout) => this.mapDatabaseToWorkout(workout));
  },

  // Get monthly analytics
  async getMonthlyAnalytics(month: number, year: number): Promise<MonthlyAnalytics> {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      return this.getEmptyAnalytics(month, year);
    }

    const startDate = new Date(year, month, 1).toISOString().split('T')[0];
    const endDate = new Date(year, month + 1, 0).toISOString().split('T')[0];

    const { data: workouts, error } = await supabase
      .from('workouts')
      .select(`
        *,
        exercise_sessions (
          *,
          workout_sets (*)
        )
      `)
      .eq('user_id', user.id)
      .gte('date', startDate)
      .lte('date', endDate);

    if (error) {
      console.error('Error fetching monthly analytics:', error);
      throw error;
    }

    const mappedWorkouts : Workout[] = workouts.map((workout : Workout) => this.mapDatabaseToWorkout(workout));
    
    const totalSets = mappedWorkouts.reduce((sum : number, workout : Workout) => sum + workout.totalSets, 0);
    const totalReps = mappedWorkouts.reduce((sum : number, workout : Workout) => sum + workout.totalReps, 0);

    const muscleGroups: Record<string, number> = {};
    const exerciseMaxWeights: Record<string, number> = {};

    mappedWorkouts.forEach(workout  => {
      muscleGroups[workout.muscleGroup] = (muscleGroups[workout.muscleGroup] || 0) + 1;
      
      workout.exercises.forEach(exercise => {
        const currentMax = exerciseMaxWeights[exercise.exerciseName] || 0;
        exerciseMaxWeights[exercise.exerciseName] = Math.max(currentMax, exercise.maxWeight);
      });
    });

    const totalDuration = mappedWorkouts.reduce((sum : number, workout : Workout) => {
      const start = new Date(`${workout.date} ${workout.startTime}`);
      const end = new Date(`${workout.date} ${workout.endTime}`);
      return sum + (end.getTime() - start.getTime());
    }, 0);

    const averageWorkoutDuration = mappedWorkouts.length > 0 ? totalDuration / mappedWorkouts.length / (1000 * 60) : 0;

    return {
      month: new Date(year, month).toLocaleString('default', { month: 'long' }),
      year,
      totalWorkouts: mappedWorkouts.length,
      totalSets,
      totalReps,
      muscleGroups,
      exerciseMaxWeights,
      averageWorkoutDuration
    };
  },

  // Update workout (for editing)
  async updateWorkout(workoutId: string, updates: Partial<Omit<Workout, 'id'>>): Promise<Workout> {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      throw new Error('User must be authenticated to update workouts');
    }

    const updateData: any = {};
    
    if (updates.date) updateData.date = updates.date;
    if (updates.startTime) updateData.start_time = updates.startTime;
    if (updates.endTime) updateData.end_time = updates.endTime;
    if (updates.muscleGroup) updateData.muscle_group = updates.muscleGroup;
    if (updates.totalSets !== undefined) updateData.total_sets = updates.totalSets;
    if (updates.totalReps !== undefined) updateData.total_reps = updates.totalReps;

    const { data: workoutData, error: workoutError } = await supabase
      .from('workouts')
      .update(updateData)
      .eq('id', workoutId)
      .eq('user_id', user.id)
      .select()
      .single();

    if (workoutError) {
      console.error('Error updating workout:', workoutError);
      throw workoutError;
    }

    // If exercises are provided, update them too
    if (updates.exercises) {
      // Delete existing exercise sessions and sets
      await supabase
        .from('exercise_sessions')
        .delete()
        .eq('workout_id', workoutId);

      // Create new exercise sessions and sets
      const exerciseSessionPromises = updates.exercises.map(async (exercise) => {
        const { data: sessionData, error: sessionError } = await supabase
          .from('exercise_sessions')
          .insert({
            workout_id: workoutId,
            exercise_id: exercise.exerciseId,
            exercise_name: exercise.exerciseName,
            start_time: exercise.startTime,
            end_time: exercise.endTime,
            muscle_group: exercise.muscleGroup,
            max_weight: exercise.maxWeight
          })
          .select()
          .single();

        if (sessionError) {
          console.error('Error creating exercise session:', sessionError);
          throw sessionError;
        }

        // Save workout sets for this exercise session
        const setPromise = exercise.sets.map(async (set, setIndex) => {
          const { data: setData, error: setError } = await supabase
            .from('workout_sets')
            .insert({
              exercise_session_id: sessionData.id,
              reps: set.reps,
              weight: set.weight,
              set_order: setIndex + 1
            })
            .select()
            .single();

          if (setError) {
            console.error('Error creating workout set:', setError);
            throw setError;
          }

          return setData;
        });

        const sets = await Promise.all(setPromise);
        
        return {
          ...this.mapDatabaseToExerciseSession(sessionData),
          sets: sets.map((set) => this.mapDatabaseToWorkoutSet(set))
        };
      });

      const exercises = await Promise.all(exerciseSessionPromises);

      return {
        id: workoutData.id,
        date: workoutData.date,
        startTime: workoutData.start_time,
        endTime: workoutData.end_time || '',
        muscleGroup: workoutData.muscle_group,
        totalSets: workoutData.total_sets,
        totalReps: workoutData.total_reps,
        exercises
      };
    }

    // If no exercises update, fetch the current workout with exercises
    const { data: fullWorkout, error: fetchError } = await supabase
      .from('workouts')
      .select(`
        *,
        exercise_sessions (
          *,
          workout_sets (*)
        )
      `)
      .eq('id', workoutId)
      .eq('user_id', user.id)
      .single();

    if (fetchError) {
      console.error('Error fetching updated workout:', fetchError);
      throw fetchError;
    }

    return this.mapDatabaseToWorkout(fullWorkout);
  },

  // Delete workout
  async deleteWorkout(workoutId: string): Promise<void> {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      throw new Error('User must be authenticated to delete workouts');
    }

    const { error } = await supabase
      .from('workouts')
      .delete()
      .eq('id', workoutId);

    if (error) {
      console.error('Error deleting workout:', error);
      throw error;
    }
  },

  // Helper functions
  mapDatabaseToWorkout(dbWorkout: any): Workout {
    return {
      id: dbWorkout.id,
      date: dbWorkout.date,
      startTime: dbWorkout.start_time,
      endTime: dbWorkout.end_time || '',
      muscleGroup: dbWorkout.muscle_group,
      totalSets: dbWorkout.total_sets,
      totalReps: dbWorkout.total_reps,
      exercises: (dbWorkout.exercise_sessions || []).map((session: any) => ({
        ...this.mapDatabaseToExerciseSession(session),
        sets: (session.workout_sets || [])
          .sort((a: any, b: any) => a.set_order - b.set_order)
          .map((set: any) => this.mapDatabaseToWorkoutSet(set))
      }))
    };
  },

  mapDatabaseToExerciseSession(dbSession: DatabaseExerciseSession): Omit<ExerciseSession, 'sets'> {
    return {
      id: dbSession.id,
      exerciseId: dbSession.exercise_id,
      exerciseName: dbSession.exercise_name,
      startTime: dbSession.start_time,
      endTime: dbSession.end_time || '',
      muscleGroup: dbSession.muscle_group,
      maxWeight: dbSession.max_weight
    };
  },

  mapDatabaseToWorkoutSet(dbSet: WorkoutSet): WorkoutSet {
    return {
      reps: dbSet.reps,
      weight: dbSet.weight
    };
  },

  getEmptyAnalytics(month: number, year: number): MonthlyAnalytics {
    return {
      month: new Date(year, month).toLocaleString('default', { month: 'long' }),
      year,
      totalWorkouts: 0,
      totalSets: 0,
      totalReps: 0,
      muscleGroups: {},
      exerciseMaxWeights: {},
      averageWorkoutDuration: 0
    };
  }
};