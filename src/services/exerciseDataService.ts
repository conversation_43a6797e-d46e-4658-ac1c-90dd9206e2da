import { Exercise } from '../types';

// Interface for the raw exercise data from JSON files
interface RawExerciseData {
  name: string;
  force?: string;
  level: string;
  mechanic?: string | null;
  equipment: string;
  primaryMuscles: string[];
  secondaryMuscles: string[];
  instructions: string[];
  category: string;
  images: string[];
  id: string;
}

// Interface for grouped exercises by primary muscle and equipment
export interface ExercisesByPrimaryMuscleAndEquipment {
  [primaryMuscle: string]: {
    [equipment: string]: Exercise[];
  };
}

// Equipment mapping to standardize equipment names
const EQUIPMENT_MAP: Record<string, string> = {
  'barbell': 'barbell',
  'dumbbell': 'dumbbell',
  'dumbbells': 'dumbbell',
  'cable': 'cable',
  'machine': 'machine',
  'body only': 'bodyweight',
  'bodyweight': 'bodyweight',
  'none': 'bodyweight',
  'kettlebell': 'kettlebell',
  'bands': 'bands',
  'exercise ball': 'exercise ball',
  'foam roll': 'foam roll',
  'medicine ball': 'medicine ball',
  'stability ball': 'stability ball',
  'other': 'other',
  'ez barbell': 'barbell'
};

// Primary muscle mapping to standardize muscle names
const PRIMARY_MUSCLE_MAP: Record<string, string> = {
  'quadriceps': 'quadriceps',
  'hamstrings': 'hamstrings',
  'calves': 'calves',
  'glutes': 'glutes',
  'abdominals': 'abdominals',
  'obliques': 'obliques',
  'lower back': 'lower back',
  'lats': 'lats',
  'middle back': 'middle back',
  'traps': 'traps',
  'shoulders': 'shoulders',
  'chest': 'chest',
  'triceps': 'triceps',
  'biceps': 'biceps',
  'forearms': 'forearms',
  'neck': 'neck',
  'abductors': 'abductors',
  'adductors': 'adductors'
};

export class ExerciseDataService {
  private exerciseCache: Exercise[] | null = null;
  private groupedCache: ExercisesByPrimaryMuscleAndEquipment | null = null;

  /**
   * Load all exercise JSON files from the exercises directory
   */
  async loadAllExercises(): Promise<Exercise[]> {
    if (this.exerciseCache) {
      return this.exerciseCache;
    }

    try {
      // Get list of all exercise files
      const exerciseFiles = await this.getExerciseFileList();
      const exercises: Exercise[] = [];

      // Load exercises in batches to avoid overwhelming the browser
      const batchSize = 50;
      for (let i = 0; i < exerciseFiles.length; i += batchSize) {
        const batch = exerciseFiles.slice(i, i + batchSize);
        const batchPromises = batch.map(async (fileName) => {
          try {
            const response = await fetch(`/exercises/${fileName}`);
            if (response.ok) {
              const rawData: RawExerciseData = await response.json();
              return this.mapRawDataToExercise(rawData);
            } else {
              console.warn(`Failed to load exercise file ${fileName}: ${response.status} ${response.statusText}`);
            }
          } catch (error) {
            console.warn(`Failed to load exercise file ${fileName}:`, error);
          }
          return null;
        });

        const batchResults = await Promise.all(batchPromises);
        const validExercises = batchResults.filter((ex): ex is Exercise => ex !== null);
        exercises.push(...validExercises);

        // Log progress
        console.log(`Loaded batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(exerciseFiles.length / batchSize)}: ${validExercises.length} exercises`);
      }

      this.exerciseCache = exercises;
      console.log(`✅ Loaded ${exercises.length} total exercises from JSON files`);
      return exercises;
    } catch (error) {
      console.error('Error loading exercises:', error);
      return [];
    }
  }

  /**
   * Get list of exercise JSON files from the manifest
   */
  private async getExerciseFileList(): Promise<string[]> {
    try {
      const response = await fetch('/exercise-manifest.json');
      if (response.ok) {
        const manifest = await response.json();
        console.log(`📋 Found manifest with ${manifest.totalFiles} exercise files`);
        return manifest.files || [];
      } else {
        console.error('Failed to fetch exercise manifest:', response.status);
      }
    } catch (error) {
      console.error('Error loading exercise manifest:', error);
    }

    // Fallback: return empty array
    console.warn('⚠️ No exercise manifest found, no exercises will be loaded');
    return [];
  }

  /**
   * Map raw exercise data to our Exercise interface
   */
  private mapRawDataToExercise(rawData: RawExerciseData): Exercise | null {
    try {
      if (!rawData.name || !rawData.primaryMuscles || rawData.primaryMuscles.length === 0) {
        return null;
      }

      const primaryMuscle = rawData.primaryMuscles[0].toLowerCase().trim();
      const equipment = (rawData.equipment || 'other').toLowerCase().trim();

      // Use the ID from JSON file (which matches folder names) or generate from name
      const id = rawData.id || rawData.name.toLowerCase()
        .replace(/[^a-z0-9\s]/g, '') // Remove special characters
        .replace(/\s+/g, '-') // Replace spaces with hyphens
        .trim();

      return {
        id, // This will be used for image loading and should match folder names
        name: rawData.name, // Keep the human-readable name for display
        muscleGroup: PRIMARY_MUSCLE_MAP[primaryMuscle] || primaryMuscle,
        equipment: EQUIPMENT_MAP[equipment] || equipment,
        description: rawData.instructions?.join(' ') || 'No description available',
        instructions: rawData.instructions,
        primaryMuscles: rawData.primaryMuscles,
        secondaryMuscles: rawData.secondaryMuscles,
        level: rawData.level,
        mechanic: rawData.mechanic,
        force: rawData.force,
        category: rawData.category
      };
    } catch (error) {
      console.error('Error mapping exercise data:', error, rawData);
      return null;
    }
  }

  /**
   * Group all exercises by primary muscle and then by equipment
   * This follows the exact requirement: group by primaryMuscles property, then by equipment
   */
  async getExercisesGroupedByPrimaryMuscleAndEquipment(): Promise<ExercisesByPrimaryMuscleAndEquipment> {
    if (this.groupedCache) {
      return this.groupedCache;
    }

    const exercises = await this.loadAllExercises();
    const grouped: ExercisesByPrimaryMuscleAndEquipment = {};

    exercises.forEach(exercise => {
      // Use the first primaryMuscles from the JSON data (requirement #1 & #2)
      const primaryMuscle = exercise.primaryMuscles?.[0];

      if (!primaryMuscle) {
        console.warn('Exercise missing primaryMuscles:', exercise.name);
        return; // Skip exercises without primaryMuscles
      }

      const equipment = exercise.equipment || 'other';

      // Group by primaryMuscle (requirement #2)
      if (!grouped[primaryMuscle]) {
        grouped[primaryMuscle] = {};
      }

      // Then group by equipment within each muscle group (requirement #4)
      if (!grouped[primaryMuscle][equipment]) {
        grouped[primaryMuscle][equipment] = [];
      }

      grouped[primaryMuscle][equipment].push(exercise);
    });

    // Sort exercises within each equipment group
    Object.keys(grouped).forEach(muscle => {
      Object.keys(grouped[muscle]).forEach(equipment => {
        grouped[muscle][equipment].sort((a, b) => a.name.localeCompare(b.name));
      });
    });

    this.groupedCache = grouped;
    console.log(`✅ Grouped exercises by primary muscle:`, Object.keys(grouped));
    return grouped;
  }

  /**
   * Get exercises for a specific primary muscle group
   */
  async getExercisesByPrimaryMuscle(primaryMuscle: string): Promise<{ [equipment: string]: Exercise[] }> {
    const allGrouped = await this.getExercisesGroupedByPrimaryMuscleAndEquipment();
    return allGrouped[primaryMuscle] || {};
  }

  /**
   * Get exercises for multiple primary muscle groups combined
   */
  async getExercisesByMuscleGroup(muscles: string[]): Promise<{ [equipment: string]: Exercise[] }> {
    const allGrouped = await this.getExercisesGroupedByPrimaryMuscleAndEquipment();
    const combinedExercises: { [equipment: string]: Exercise[] } = {};

    muscles.forEach(muscle => {
      const muscleExercises = allGrouped[muscle] || {};
      Object.entries(muscleExercises).forEach(([equipment, exercises]) => {
        if (!combinedExercises[equipment]) {
          combinedExercises[equipment] = [];
        }
        combinedExercises[equipment].push(...exercises);
      });
    });

    // Remove duplicates based on exercise ID
    Object.keys(combinedExercises).forEach(equipment => {
      const uniqueExercises = combinedExercises[equipment].filter((exercise, index, array) =>
        array.findIndex(e => e.id === exercise.id) === index
      );
      combinedExercises[equipment] = uniqueExercises;
    });

    return combinedExercises;
  }

  /**
   * Get all available primary muscle groups
   */
  async getAvailablePrimaryMuscles(): Promise<string[]> {
    const grouped = await this.getExercisesGroupedByPrimaryMuscleAndEquipment();
    return Object.keys(grouped).sort();
  }

  /**
   * Get all available equipment types for a primary muscle
   */
  async getAvailableEquipmentForMuscle(primaryMuscle: string): Promise<string[]> {
    const muscleExercises = await this.getExercisesByPrimaryMuscle(primaryMuscle);
    return Object.keys(muscleExercises).sort();
  }

  /**
   * Clear cache to force reload
   */
  clearCache(): void {
    this.exerciseCache = null;
    this.groupedCache = null;
  }

  /**
   * Search exercises by name within a primary muscle group
   */
  async searchExercisesInMuscleGroup(primaryMuscle: string, searchTerm: string): Promise<Exercise[]> {
    const muscleExercises = await this.getExercisesByPrimaryMuscle(primaryMuscle);
    const allExercises = Object.values(muscleExercises).flat();
    
    if (!searchTerm.trim()) {
      return allExercises;
    }

    const term = searchTerm.toLowerCase();
    return allExercises.filter(exercise =>
      exercise.name.toLowerCase().includes(term)
    );
  }
}

// Export singleton instance
export const exerciseDataService = new ExerciseDataService();
