import type {DatabaseExercise} from '../lib/supabase';
import {supabase} from '../lib/supabase';
import {Exercise, ExerciseSummary, JsonExercise, MuscleGroup} from '../types';
// adda const top
const DEFAULT_EXERCISES: Record<MuscleGroup, Exercise[]> = {
  chest: [
    { id: 'chest-1', name: 'Push-ups', muscleGroup: 'chest', equipment: 'bodyweight', description: 'Classic push-up exercise' },
    { id: 'chest-2', name: 'Bench Press', muscleGroup: 'chest', equipment: 'barbell', description: 'Barbell bench press' },
    { id: 'chest-3', name: 'Dumbbell Flyes', muscleGroup: 'chest', equipment: 'dumbbell', description: 'Dumbbell fly exercise' },
    { id: 'chest-4', name: 'Chest Press Machine', muscleGroup: 'chest', equipment: 'machine', description: 'Machine chest press' }
  ],
  back: [
    { id: 'back-1', name: 'Pull-ups', muscleGroup: 'back', equipment: 'bodyweight', description: 'Classic pull-up exercise' },
    { id: 'back-2', name: 'Bent Over Row', muscleGroup: 'back', equipment: 'barbell', description: 'Barbell bent over row' },
    { id: 'back-3', name: 'One Arm Row', muscleGroup: 'back', equipment: 'dumbbell', description: 'Dumbbell one arm row' },
    { id: 'back-4', name: 'Lat Pulldown', muscleGroup: 'back', equipment: 'machine', description: 'Machine lat pulldown' }
  ],
  legs: [
    { id: 'legs-1', name: 'Bodyweight Squats', muscleGroup: 'legs', equipment: 'bodyweight', description: 'Classic squat exercise' },
    { id: 'legs-2', name: 'Barbell Squats', muscleGroup: 'legs', equipment: 'barbell', description: 'Barbell back squats' },
    { id: 'legs-3', name: 'Lunges', muscleGroup: 'legs', equipment: 'dumbbell', description: 'Dumbbell lunges' },
    { id: 'legs-4', name: 'Leg Press', muscleGroup: 'legs', equipment: 'machine', description: 'Machine leg press' }
  ],
  shoulders: [
    { id: 'shoulders-1', name: 'Pike Push-ups', muscleGroup: 'shoulders', equipment: 'bodyweight', description: 'Pike push-up exercise' },
    { id: 'shoulders-2', name: 'Military Press', muscleGroup: 'shoulders', equipment: 'barbell', description: 'Barbell military press' },
    { id: 'shoulders-3', name: 'Lateral Raises', muscleGroup: 'shoulders', equipment: 'dumbbell', description: 'Dumbbell lateral raises' },
    { id: 'shoulders-4', name: 'Shoulder Press Machine', muscleGroup: 'shoulders', equipment: 'machine', description: 'Machine shoulder press' }
  ],
  core: [
    { id: 'core-1', name: 'Crunches', muscleGroup: 'core', equipment: 'bodyweight', description: 'Basic crunches' },
    { id: 'core-2', name: 'Russian Twists', muscleGroup: 'core', equipment: 'dumbbell', description: 'Weighted russian twists' },
    { id: 'core-3', name: 'Cable Woodchoppers', muscleGroup: 'core', equipment: 'machine', description: 'Cable woodchopper exercise' },
    { id: 'core-4', name: 'Plank', muscleGroup: 'core', equipment: 'bodyweight', description: 'Plank hold exercise' }
  ],
  glutes: [
    { id: 'glutes-1', name: 'Glute Bridges', muscleGroup: 'glutes', equipment: 'bodyweight', description: 'Basic glute bridge' },
    { id: 'glutes-2', name: 'Hip Thrust', muscleGroup: 'glutes', equipment: 'barbell', description: 'Barbell hip thrust' },
    { id: 'glutes-3', name: 'Bulgarian Split Squats', muscleGroup: 'glutes', equipment: 'dumbbell', description: 'Dumbbell bulgarian split squats' },
    { id: 'glutes-4', name: 'Cable Kickbacks', muscleGroup: 'glutes', equipment: 'machine', description: 'Cable glute kickbacks' }
  ]
};

// GitHub exercise database interface

export const exerciseService = {

  // Fetch exercises from the GitHub repository
  fetchExercisesJson: async function (url: string = '/exercises.json') : Promise<JsonExercise[] > {
    try {
      console.log('Fetching exercises from:', url);
      const response = await fetch(url);
      if (!response.ok) {
        console.error(`Failed to fetch exercises: ${response.status}`);
        throw new Error(`Failed to fetch: ${response.status}`);
      }
      const data = await response.json();
      console.log(`Successfully loaded ${data.length} exercises from JSON`);
      return data;
    } catch (error) {
      console.error('Error fetching exercises JSON:', error);
      return [];
    }
  },

  getExerciseSummaries: function(data: JsonExercise[] | JsonExercise): ExerciseSummary[] {
  const array = Array.isArray(data) ? data : [data];
    return array.map(ex => ({
    name: ex.name,    primary_muscles: ex.primary_muscles,
    }));
  },

  fetchAndGetSummaries: async function(url: string = '/exercises.json'): Promise<ExerciseSummary[] | null> {
    try {
      let data: JsonExercise[] | undefined;
      data = await this.fetchExercisesJson(url);
      if (!data || data.length === 0) {
        console.warn('No exercise data found, returning null');
        return null;
      }
      return this.getExerciseSummaries(data);
    } catch (error) {
      console.error('Error fetching or processing exercises:', error);
      return null;
    }
  },

  // Map GitHub exercise to our Exercise type
  mapGitHubToExercise(githubExercise: JsonExercise): Exercise | null {
  try {
    // Verificação inicial mais robusta
    if (!githubExercise ||
        !githubExercise.name ||
        !githubExercise.primary_muscles ||
        !Array.isArray(githubExercise.primary_muscles) ||
        githubExercise.primary_muscles.length === 0) {
      console.log('Exercício inválido ou sem músculos primários:', githubExercise);
      return null;
    }

    // Mapeamento expandido de equipamentos
    const equipmentMap: Record<string, string> = {
      'dumbbell': 'dumbbell',
      'dumbbells': 'dumbbell',
      'barbell': 'barbell',
      'ez barbell': 'barbell',
      'cable': 'machine',
      'machine': 'machine',
      'body only': 'bodyweight',
      'bodyweight': 'bodyweight',
      'none': 'bodyweight',
      'kettlebell': 'dumbbell',
      'bands': 'machine',
      'exercise ball': 'bodyweight',
      'foam roll': 'bodyweight',
      'medicine ball': 'dumbbell',
      'stability ball': 'bodyweight'
    };

    const equipment = equipmentMap[
      (githubExercise.equipment || 'bodyweight').toLowerCase().trim()
    ] || 'bodyweight';

    // Expandir o mapeamento de músculos
    const muscleGroupMap: Record<string, MuscleGroup> = {
      // Pernas
      'quadriceps': 'legs',
      'hamstrings': 'legs',
      'calves': 'legs',
      'adductors': 'legs',
      'quads': 'legs',

      // Glúteos
      'glutes': 'glutes',
      'gluteus maximus': 'glutes',
      'gluteus medius': 'glutes',

      // Core
      'abdominals': 'core',
      'obliques': 'core',
      'abs': 'core',
      'core': 'core',

      // Costas
      'lower back': 'back',
      'lats': 'back',
      'latissimus dorsi': 'back',
      'middle back': 'back',
      'traps': 'back',
      'trapezius': 'back',
      'rhomboids': 'back',
      'erector spinae': 'back',

      // Ombros
      'shoulders': 'shoulders',
      'deltoids': 'shoulders',
      'anterior deltoid': 'shoulders',
      'posterior deltoid': 'shoulders',
      'lateral deltoid': 'shoulders',
      'delts': 'shoulders',

      // Peito
      'chest': 'chest',
      'pectorals': 'chest',
      'pectoralis major': 'chest',
      'pecs': 'chest'
    };

    const primaryMuscle = (githubExercise.primary_muscles[0] || '').toLowerCase().trim();
    const muscleGroup = muscleGroupMap[primaryMuscle];

    if (!muscleGroup) {
      console.log('Músculo não mapeado:', primaryMuscle, 'para exercício:', githubExercise.name);
      return null;
    }

    return {
      id: `github-${githubExercise.id}`,
      name: githubExercise.name,
      muscleGroup,
      equipment,
      description: githubExercise.instructions?.join(' ') ||
                  githubExercise.description ||
                  'Descrição não disponível'
    };
  } catch (error) {
    console.error('Erro ao mapear exercício do GitHub:', error, githubExercise);
    return null;
  }
},

  // Get GitHub exercises by muscle group
  async getGitHubExercisesByMuscleGroup(muscleGroup: MuscleGroup): Promise<Exercise[]> {
    try {
      const githubExercises = await this.fetchExercisesJson();

      if (!Array.isArray(githubExercises) || githubExercises.length === 0) {
        console.warn('No GitHub exercises available, using fallback');
        return this.getFallbackExercisesByMuscleGroup(muscleGroup);
      }

      const mappedExercises = githubExercises
        .map(ex => this.mapGitHubToExercise(ex))
        .filter((ex): ex is Exercise => ex !== null && ex.muscleGroup === muscleGroup);

      console.log(`Found ${mappedExercises.length} ${muscleGroup} exercises from GitHub`);

      // If no exercises found for this muscle group, use fallback
      if (mappedExercises.length === 0) {
        console.log(`No GitHub exercises found for ${muscleGroup}, using fallback`);
        return this.getFallbackExercisesByMuscleGroup(muscleGroup);
      }

      return mappedExercises;
    } catch (error) {
      console.error('Error fetching GitHub exercises by muscle group:', error);
      return this.getFallbackExercisesByMuscleGroup(muscleGroup);
    }
  },

  // Get all exercises grouped by primary muscle
  async getAllExercisesGroupedByPrimaryMuscle(): Promise<Record<string, Exercise[]>> {
    const allExercises = await this.getAllExercises();
    return this.groupExercisesByPrimaryMuscle(allExercises);
  },
  
  // Group exercises by primary muscle
  groupExercisesByPrimaryMuscle(exercises: Exercise[]): Record<string, Exercise[]> {
    return exercises.reduce((grouped, exercise) => {
      // Get primary muscle from exercise
      const primaryMuscle = exercise.primaryMuscles && exercise.primaryMuscles.length > 0 
        ? exercise.primaryMuscles[0] 
        : exercise.muscleGroup;
      
      if (!grouped[primaryMuscle]) {
        grouped[primaryMuscle] = [];
      }
      
      grouped[primaryMuscle].push(exercise);
      return grouped;
    }, {} as Record<string, Exercise[]>);
  },
  
  // Get all exercises (GitHub + user custom)
  async getAllExercises(): Promise<Exercise[]> {
    try {
      // Get GitHub exercises
      const githubExercises = await this.fetchExercisesJson();
      let mappedGitHubExercises: Exercise[] = [];

      if (Array.isArray(githubExercises) && githubExercises.length > 0) {
        try {
          mappedGitHubExercises = githubExercises
            .map(ex => this.mapGitHubToExercise(ex))
            .filter((ex): ex is Exercise => ex !== null);

          console.log(`Mapped ${mappedGitHubExercises.length} GitHub exercises`);
        } catch (mappingError) {
          console.error('Error mapping GitHub exercises:', mappingError);
          mappedGitHubExercises = this.getFallbackExercises();
        }
      } else {
        console.log('No GitHub exercises available, using fallback exercises');
        mappedGitHubExercises = this.getFallbackExercises();
      }

      // Get custom exercises from database
      const { data: customExercises, error } = await supabase
        .from('exercises')
        .select('*')
        .eq('is_default', false)
        .order('name');

      if (error) {
        console.error('Error fetching custom exercises:', error);
        return mappedGitHubExercises;
      }

      const mappedCustomExercises  = (customExercises || []).map((ex: DatabaseExercise) => this.mapDatabaseToExercise(ex));
      console.log(`Found ${mappedCustomExercises.length} custom exercises`);

      // Combine and sort
      const allExercises = [...mappedGitHubExercises, ...mappedCustomExercises]
        .sort((a, b) => a.name.localeCompare(b.name));

      console.log(`Total exercises available: ${allExercises.length}`);
      return allExercises;
    } catch (error) {
      console.error('Error fetching all exercises:', error);
      return this.getFallbackExercises();
    }
  },

  // ... outros métodos ...

  // Get exercises by muscle group combining GitHub, default, and custom exercises
  async getExercisesByMuscleGroup(muscleGroup: MuscleGroup): Promise<Exercise[]> {
    try {
      console.log(`Fetching exercises for ${muscleGroup}...`);

      // First, try to get GitHub exercises
      const githubExercises = await this.getGitHubExercisesByMuscleGroup(muscleGroup);
      console.log(`GitHub exercises found: ${githubExercises.length}`);
      
      // Get default exercises as fallback
      const defaultExercises = DEFAULT_EXERCISES[muscleGroup] || [];
      console.log(`Default exercises found: ${defaultExercises.length}`);

      // Try to fetch custom exercises
      const { data: customExercises, error } = await supabase
        .from('exercises')
        .select('*')
        .eq('muscle_group', muscleGroup)
        .eq('is_default', false);

      if (error) {
        console.error('Error fetching custom exercises:', error);
        console.log('Returning GitHub and default exercises only');
        // Group exercises by equipment type
        return this.groupExercisesByEquipment([...githubExercises, ...defaultExercises]);
      }

      const mappedCustomExercises = customExercises
        ? customExercises.map((ex: DatabaseExercise) => this.mapDatabaseToExercise(ex))
        : [];

      console.log(`Custom exercises found: ${mappedCustomExercises.length}`);
      
      // Combine all exercises
      const allExercises = [...githubExercises, ...defaultExercises, ...mappedCustomExercises];
      console.log(`Total exercises found for ${muscleGroup}: ${allExercises.length}`);
      
      // Group exercises by equipment type
      return this.groupExercisesByEquipment(allExercises);
    } catch (error) {
      console.error('Error fetching exercises by muscle group:', error);
      return this.getFallbackExercisesByMuscleGroup(muscleGroup);
    }
  },
  
  // Group exercises by equipment type
  groupExercisesByEquipment(exercises: Exercise[]): Exercise[] {
    // Sort exercises by equipment for better grouping in UI
    return exercises.sort((a, b) => {
      // First sort by equipment
      const equipmentOrder = ['bodyweight', 'dumbbell', 'barbell', 'machine', 'other'];
      const equipmentA = a.equipment || 'other';
      const equipmentB = b.equipment || 'other';
      
      const equipmentIndexA = equipmentOrder.indexOf(equipmentA) !== -1 ? 
        equipmentOrder.indexOf(equipmentA) : equipmentOrder.length;
      const equipmentIndexB = equipmentOrder.indexOf(equipmentB) !== -1 ? 
        equipmentOrder.indexOf(equipmentB) : equipmentOrder.length;
      
      if (equipmentIndexA !== equipmentIndexB) {
        return equipmentIndexA - equipmentIndexB;
      }
      
      // Then sort by name
      return a.name.localeCompare(b.name);
    });
},

  // Fallback exercises when GitHub API fails
  getFallbackExercisesByMuscleGroup(muscleGroup: MuscleGroup): Exercise[] {
    return DEFAULT_EXERCISES[muscleGroup] || [];
  },

  getFallbackExercises(): Exercise[] {
    const allMuscleGroups: MuscleGroup[] = ['legs', 'glutes', 'core', 'back', 'shoulders', 'chest'];
    return allMuscleGroups.flatMap(group => this.getFallbackExercisesByMuscleGroup(group));
  },

  // Create custom exercise
  async createCustomExercise(exercise: Omit<Exercise, 'id'>): Promise<Exercise> {
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      throw new Error('User must be authenticated to create custom exercises');
    }

    const { data, error } = await supabase
      .from('exercises')
      .insert({
        name: exercise.name,
        muscle_group: exercise.muscleGroup,
        equipment: exercise.equipment,
        description: exercise.description,
        created_by: user.id,
        is_default: false
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating custom exercise:', error);
      throw error;
    }

    return this.mapDatabaseToExercise(data);
  },

  // Update custom exercise
  async updateCustomExercise(id: string, updates: Partial<Omit<Exercise, 'id'>>): Promise<Exercise> {
    const updateData: Exercise = {
      id: '',
      name: '',
      muscleGroup: '',
      equipment: '',
      description: ''
    };

    if (updates.name) updateData.name = updates.name;
    if (updates.muscleGroup) updateData.muscleGroup = updates.muscleGroup;
    if (updates.equipment) updateData.equipment = updateData.equipment;
    if (updates.description) updateData.description = updates.description;

    const { data, error } = await supabase
      .from('exercises')
      .update(updateData)
      .eq('id', id)
      .eq('is_default', false)
      .select()
      .single();

    if (error) {
      console.error('Error updating custom exercise:', error);
      throw error;
    }

    return this.mapDatabaseToExercise(data);
  },

  // Delete custom exercise
  async deleteCustomExercise(id: string): Promise<void> {
    const { error } = await supabase
      .from('exercises')
      .delete()
      .eq('id', id)
      .eq('is_default', false);

    if (error) {
      console.error('Error deleting custom exercise:', error);
      throw error;
    }
  },

  // Get exercise by ID
  async getExerciseById(id: string): Promise<Exercise | null> {
    // Check if it's a GitHub exercise
    if (id.startsWith('github-')) {
      const githubId = id.replace('github-', '');
      const githubExercises = await this.fetchExercisesJson();
      const githubExercise = githubExercises.find(ex => ex.id === githubId);

      if (githubExercise) {
        return this.mapGitHubToExercise(githubExercise);
      }
    }

    // Check database for custom exercise
    const { data, error } = await supabase
      .from('exercises')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null;
      }
      console.error('Error fetching exercise by ID:', error);
      throw error;
    }

    return this.mapDatabaseToExercise(data);
  },

  // Helper function to map database exercise to app exercise
  mapDatabaseToExercise(dbExercise: DatabaseExercise): Exercise {
    return {
      id: dbExercise.id,
      name: dbExercise.name,
      muscleGroup: dbExercise.muscle_group as MuscleGroup,
      equipment: dbExercise.equipment,
      description: dbExercise.description
    };
  }
};