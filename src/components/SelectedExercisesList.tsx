import React from 'react';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import {
  useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { GripVertical, X, Eye } from 'lucide-react';
import { Exercise } from '../types';
import { getImageFromData, getExerciseSpecificFallback } from '../utils/exerciseImages';

interface SelectedExercisesListProps {
  selectedExerciseIds: string[];
  exercises: Exercise[];
  onReorder: (newOrder: string[]) => void;
  onRemove: (exerciseId: string) => void;
  onViewDetails: (exercise: Exercise) => void;
}

interface SortableExerciseItemProps {
  exercise: Exercise;
  index: number;
  onRemove: (exerciseId: string) => void;
  onViewDetails: (exercise: Exercise) => void;
}

const SortableExerciseItem: React.FC<SortableExerciseItemProps> = ({
  exercise,
  index,
  onRemove,
  onViewDetails,
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: exercise.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`bg-gray-700 border border-gray-600 rounded-lg p-3 flex items-center gap-3 ${
        isDragging ? 'shadow-lg' : ''
      }`}
    >
      {/* Drag Handle */}
      <div
        {...attributes}
        {...listeners}
        className="cursor-grab active:cursor-grabbing text-gray-400 hover:text-white transition-colors"
      >
        <GripVertical size={20} />
      </div>

      {/* Exercise Order Number */}
      <div className="flex-shrink-0 w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-semibold">
        {index + 1}
      </div>

      {/* Exercise Image */}
      <div className="flex-shrink-0 w-12 h-12 rounded-lg overflow-hidden bg-gray-600 flex items-center justify-center">
        <img
          src={getImageFromData(exercise.id)}
          alt={exercise.name}
          className="max-w-full max-h-full object-contain"
          onError={(e) => {
            const target = e.currentTarget as HTMLImageElement;
            target.src = getExerciseSpecificFallback(
              exercise.id,
              exercise.muscleGroup,
              exercise.equipment
            );
          }}
        />
      </div>

      {/* Exercise Info */}
      <div className="flex-1 min-w-0">
        <h4 className="text-white font-medium text-sm truncate">{exercise.name}</h4>
        <div className="flex items-center gap-2 mt-1">
          <span className="text-xs bg-gray-600 text-gray-300 px-2 py-1 rounded">
            {exercise.equipment}
          </span>
          {exercise.level && (
            <span className="text-xs bg-blue-600 text-white px-2 py-1 rounded">
              {exercise.level}
            </span>
          )}
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex items-center gap-2">
        <button
          onClick={() => onViewDetails(exercise)}
          className="p-2 text-gray-400 hover:text-white transition-colors"
          title="View details"
        >
          <Eye size={16} />
        </button>
        <button
          onClick={() => onRemove(exercise.id)}
          className="p-2 text-gray-400 hover:text-red-400 transition-colors"
          title="Remove from workout"
        >
          <X size={16} />
        </button>
      </div>
    </div>
  );
};

const SelectedExercisesList: React.FC<SelectedExercisesListProps> = ({
  selectedExerciseIds,
  exercises,
  onReorder,
  onRemove,
  onViewDetails,
}) => {
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Get exercises in the correct order
  const orderedExercises = selectedExerciseIds
    .map(id => exercises.find(ex => ex.id === id))
    .filter((ex): ex is Exercise => ex !== undefined);

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      const oldIndex = selectedExerciseIds.indexOf(active.id as string);
      const newIndex = selectedExerciseIds.indexOf(over.id as string);
      
      const newOrder = arrayMove(selectedExerciseIds, oldIndex, newIndex);
      onReorder(newOrder);
    }
  };

  if (selectedExerciseIds.length === 0) {
    return null;
  }

  return (
    <div className="bg-gray-800 border border-gray-700 rounded-lg p-4">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-white">
          Selected Exercises ({selectedExerciseIds.length})
        </h3>
        <p className="text-sm text-gray-400">
          Drag to reorder execution sequence
        </p>
      </div>

      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragEnd={handleDragEnd}
      >
        <SortableContext
          items={selectedExerciseIds}
          strategy={verticalListSortingStrategy}
        >
          <div className="space-y-2">
            {orderedExercises.map((exercise, index) => (
              <SortableExerciseItem
                key={exercise.id}
                exercise={exercise}
                index={index}
                onRemove={onRemove}
                onViewDetails={onViewDetails}
              />
            ))}
          </div>
        </SortableContext>
      </DndContext>
    </div>
  );
};

export default SelectedExercisesList;