import React, { useState, useEffect } from 'react';
import { ArrowLeft, Search, ChevronRight, Du<PERSON>bell, Settings, User, Activity, ChevronDown, ChevronUp, Eye, X } from 'lucide-react';
import { Exercise } from '../types';
import { exerciseDataService } from '../services/exerciseDataService';
import { getImageFromData, getExerciseSpecificFallback } from '../utils/exerciseImages';
import SelectedExercisesList from './SelectedExercisesList';

// Define the same muscle groups as in PrimaryMuscleSelector
const MUSCLE_GROUPS = {
  'Legs': ['abductors', 'adductors', 'quadriceps', 'hamstrings', 'calves', 'glutes'],
  'Arms': ['biceps', 'triceps', 'forearms'],
  'Shoulders': ['shoulders', 'traps'],
  'Back': ['middle back', 'lats', 'lower back'],
  'Chest': ['chest'],
  'Core': ['abdominals']
};

interface ExercisesByEquipmentProps {
  primaryMuscle: string; // This can now be either a muscle group name or individual muscle
  onBack: () => void;
  onExercisesSelected: (exerciseIds: string[]) => void;
}

const ExercisesByEquipment: React.FC<ExercisesByEquipmentProps> = ({ 
  primaryMuscle, 
  onBack, 
  onExercisesSelected 
}) => {
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedExercises, setSelectedExercises] = useState<string[]>([]);
  const [allExercises, setAllExercises] = useState<Exercise[]>([]);
  const [exercisesByEquipment, setExercisesByEquipment] = useState<Record<string, Exercise[]>>({});
  const [expandedEquipment, setExpandedEquipment] = useState<Record<string, boolean>>({});
  const [selectedExerciseForDetails, setSelectedExerciseForDetails] = useState<Exercise | null>(null);

  useEffect(() => {
    loadExercises();
  }, [primaryMuscle]);

  const loadExercises = async () => {
    try {
      setLoading(true);

      // Check if primaryMuscle is a muscle group or individual muscle
      const muscleGroupMuscles = MUSCLE_GROUPS[primaryMuscle as keyof typeof MUSCLE_GROUPS];

      let exercises;
      if (muscleGroupMuscles) {
        // It's a muscle group - get exercises for all muscles in the group
        exercises = await exerciseDataService.getExercisesByMuscleGroup(muscleGroupMuscles);
        console.log(`✅ Loading exercises for muscle group "${primaryMuscle}" (${muscleGroupMuscles.join(', ')})`);
      } else {
        // It's an individual muscle - use the original method
        exercises = await exerciseDataService.getExercisesByPrimaryMuscle(primaryMuscle);
        console.log(`✅ Loading exercises for individual muscle "${primaryMuscle}"`);
      }

      setExercisesByEquipment(exercises);

      // Flatten all exercises for the SelectedExercisesList component
      const flatExercises = Object.values(exercises).flat() as Exercise[];
      setAllExercises(flatExercises);

      // Initialize all equipment sections as collapsed by default
      const initialExpanded = Object.keys(exercises).reduce((acc, equipment) => {
        acc[equipment] = false;
        return acc;
      }, {} as Record<string, boolean>);
      setExpandedEquipment(initialExpanded);

      console.log(`✅ Loaded exercises for ${primaryMuscle}:`, exercises);
    } catch (error) {
      console.error('Error loading exercises:', error);
    } finally {
      setLoading(false);
    }
  };

  const toggleExerciseSelection = (exerciseId: string) => {
    setSelectedExercises(prev => 
      prev.includes(exerciseId)
        ? prev.filter(id => id !== exerciseId)
        : [...prev, exerciseId]
    );
  };

  const handleReorderExercises = (newOrder: string[]) => {
    setSelectedExercises(newOrder);
  };

  const handleRemoveExercise = (exerciseId: string) => {
    setSelectedExercises(prev => prev.filter(id => id !== exerciseId));
  };

  const handleViewExerciseDetails = (exercise: Exercise) => {
    setSelectedExerciseForDetails(exercise);
  };

  const toggleEquipmentExpansion = (equipment: string) => {
    setExpandedEquipment(prev => ({
      ...prev,
      [equipment]: !prev[equipment]
    }));
  };

  const getEquipmentIcon = (equipment: string) => {
    switch (equipment.toLowerCase()) {
      case 'barbell':
        return <Dumbbell size={20} className="text-orange-400" />;
      case 'dumbbell':
        return <Dumbbell size={20} className="text-blue-400" />;
      case 'machine':
        return <Settings size={20} className="text-green-400" />;
      case 'bodyweight':
        return <User size={20} className="text-purple-400" />;
      case 'cable':
        return <Activity size={20} className="text-red-400" />;
      default:
        return <Dumbbell size={20} className="text-gray-400" />;
    }
  };

  const formatEquipmentName = (equipment: string) => {
    return equipment.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  const formatMuscleName = (muscle: string) => {
    return muscle.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  // Filter exercises based on search term
  const filteredExercisesByEquipment = Object.entries(exercisesByEquipment).reduce((filtered, [equipment, exercises]) => {
    const filteredExercises = exercises.filter(exercise =>
      exercise.name.toLowerCase().includes(searchTerm.toLowerCase())
    );
    
    if (filteredExercises.length > 0) {
      filtered[equipment] = filteredExercises;
    }
    
    return filtered;
  }, {} as Record<string, Exercise[]>);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900 text-white p-4 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading exercises...</p>
        </div>
      </div>
    );
  }


  return (
    <div className="min-h-screen bg-gray-900 text-white p-4">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="flex items-center mb-8">
          <button
            onClick={onBack}
            className="text-gray-400 hover:text-white mr-4 transition-colors"
          >
            <ArrowLeft size={24} />
          </button>
          <h1 className="text-2xl sm:text-3xl font-bold text-white">
            {MUSCLE_GROUPS[primaryMuscle as keyof typeof MUSCLE_GROUPS]
              ? `${primaryMuscle} Exercises`
              : `${formatMuscleName(primaryMuscle)} Exercises`}
          </h1>
        </div>

        {/* Search */}
        <div className="relative mb-8">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
          <input
            type="text"
            placeholder="Search exercises..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full bg-gray-800 text-white pl-10 pr-4 py-3 rounded-lg border border-gray-700 focus:border-blue-500 focus:outline-none"
          />
        </div>

        {/* Equipment Cards */}
        <div className="space-y-4">
          {Object.entries(filteredExercisesByEquipment).map(([equipment, exercises]) => (
            <div key={equipment} className="bg-gray-800 rounded-lg border border-gray-700 overflow-hidden">
              {/* Equipment Header - Collapsible */}
              <button
                onClick={() => toggleEquipmentExpansion(equipment)}
                className="w-full p-4 text-left hover:bg-gray-700 transition-colors"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    {getEquipmentIcon(equipment)}
                    <h2 className="text-xl font-bold text-white">
                      {formatEquipmentName(equipment)}
                    </h2>
                    <span className="text-sm text-gray-400 bg-gray-700 px-3 py-1 rounded-full">
                      {exercises.length} exercise{exercises.length !== 1 ? 's' : ''}
                    </span>
                  </div>
                  {expandedEquipment[equipment] ? (
                    <ChevronUp size={20} className="text-gray-400 hover:text-white transition-colors" />
                  ) : (
                    <ChevronDown size={20} className="text-gray-400 hover:text-white transition-colors" />
                  )}
                </div>
              </button>

              {/* Exercises Grid - Collapsible */}
              <div className={`transition-all duration-300 ease-in-out overflow-hidden ${
                expandedEquipment[equipment] ? 'max-h-none opacity-100' : 'max-h-0 opacity-0'
              }`}>
                <div className="p-4 pt-0">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {exercises.map((exercise) => (
                      <div key={exercise.id} className="relative">
                        <button
                          onClick={() => toggleExerciseSelection(exercise.id)}
                          className={`w-full text-left p-3 rounded-lg border transition-all relative ${
                            selectedExercises.includes(exercise.id)
                              ? 'bg-blue-600 border-blue-500'
                              : 'bg-gray-700 border-gray-600 hover:bg-gray-600'
                          }`}
                        >
                          {/* Exercise Order Badge */}
                          {selectedExercises.includes(exercise.id) && (
                            <div className="absolute top-2 left-2 w-6 h-6 bg-white text-blue-600 rounded-full flex items-center justify-center text-xs font-bold z-10">
                              {selectedExercises.indexOf(exercise.id) + 1}
                            </div>
                          )}
                          {/* Exercise Image */}
                          <div className="mb-3 flex justify-center">
                            <div className="relative w-full h-48 rounded-lg overflow-hidden bg-gray-600 flex items-center justify-center">
                              <img
                                src={getImageFromData(exercise.id)}
                                alt={exercise.name}
                                className="max-w-full max-h-full object-contain"
                                onError={(e) => {
                                  const target = e.currentTarget as HTMLImageElement;
                                  target.src = getExerciseSpecificFallback(exercise.id, exercise.muscleGroup, exercise.equipment);
                                }}
                              />
                            </div>
                          </div>

                          <div className="space-y-2">
                            <h3 className="font-medium text-white text-sm leading-tight">{exercise.name}</h3>
                            <div className="flex items-center justify-between">
                              {exercise.level && (
                                <span className="text-xs bg-gray-600 text-gray-300 px-2 py-1 rounded">
                                  {exercise.level}
                                </span>
                              )}
                              <div className="text-xs text-gray-400">
                                {exercise.equipment || 'Equipment'}
                              </div>
                            </div>
                          </div>
                        </button>

                        {/* Info Icon */}
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            setSelectedExerciseForDetails(exercise);
                          }}
                          className="absolute top-2 right-2 p-2 bg-black bg-opacity-60 hover:bg-opacity-80 rounded-full transition-all"
                          title="View exercise details"
                        >
                          <Eye size={16} className="text-white" />
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {Object.keys(filteredExercisesByEquipment).length === 0 && (
          <div className="text-center py-12">
            <div className="text-gray-400 mb-4">
              <Search size={48} className="mx-auto mb-4 opacity-50" />
              <p className="text-lg">No exercises found</p>
              <p className="text-sm">Try adjusting your search terms</p>
            </div>
          </div>
        )}

        {/* Selected Exercises List */}
        {selectedExercises.length > 0 && (
          <div className="mt-8">
            <SelectedExercisesList
              selectedExerciseIds={selectedExercises}
              exercises={allExercises}
              onReorder={handleReorderExercises}
              onRemove={handleRemoveExercise}
              onViewDetails={handleViewExerciseDetails}
            />
          </div>
        )}

        {/* Start Workout Button */}
        {selectedExercises.length > 0 && (
          <div className="fixed bottom-4 left-4 right-4 max-w-6xl mx-auto">
            <button
              onClick={() => {
                console.log('🚀 ExercisesByEquipment: Starting workout with exercise IDs in order:', selectedExercises);
                onExercisesSelected(selectedExercises);
              }}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white py-4 rounded-lg font-semibold transition-colors flex items-center justify-center gap-2"
            >
              Start Workout ({selectedExercises.length} exercises)
              <ChevronRight size={20} />
            </button>
          </div>
        )}

        {/* Exercise Details Modal */}
        {selectedExerciseForDetails && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-gray-800 rounded-lg max-w-md w-full max-h-[90vh] overflow-y-auto">
              {/* Modal Header */}
              <div className="flex items-center justify-between p-4 border-b border-gray-700">
                <h3 className="text-lg font-semibold text-white">Exercise Details</h3>
                <button
                  onClick={() => setSelectedExerciseForDetails(null)}
                  className="text-gray-400 hover:text-white transition-colors"
                >
                  <X size={24} />
                </button>
              </div>

              {/* Modal Content */}
              <div className="p-4">
                {/* Exercise Image */}
                <div className="mb-4 flex justify-center">
                  <div className="relative w-full max-w-sm h-64 rounded-lg overflow-hidden bg-gray-700 flex items-center justify-center">
                    <img
                      src={getImageFromData(selectedExerciseForDetails.id)}
                      alt={selectedExerciseForDetails.name}
                      className="max-w-full max-h-full object-contain"
                      onError={(e) => {
                        const target = e.currentTarget as HTMLImageElement;
                        target.src = getExerciseSpecificFallback(
                          selectedExerciseForDetails.id,
                          selectedExerciseForDetails.muscleGroup,
                          selectedExerciseForDetails.equipment
                        );
                      }}
                    />
                  </div>
                </div>

                {/* Exercise Info */}
                <div className="space-y-4">
                  <div>
                    <h4 className="text-xl font-semibold text-white mb-2">{selectedExerciseForDetails.name}</h4>
                    <div className="flex flex-wrap gap-2 mb-3">
                      <span className="text-xs bg-blue-600 text-white px-2 py-1 rounded">
                        {selectedExerciseForDetails.muscleGroup}
                      </span>
                      <span className="text-xs bg-green-600 text-white px-2 py-1 rounded">
                        {selectedExerciseForDetails.equipment}
                      </span>
                      {selectedExerciseForDetails.level && (
                        <span className="text-xs bg-yellow-600 text-white px-2 py-1 rounded">
                          {selectedExerciseForDetails.level}
                        </span>
                      )}
                    </div>
                  </div>

                  {/* Additional Info */}
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    {selectedExerciseForDetails.secondaryMuscles && (
                      <div>
                        <span className="text-gray-400">Secondary Muscles:</span>
                        <div className="text-white">{selectedExerciseForDetails.secondaryMuscles}</div>
                      </div>
                    )}
                    {selectedExerciseForDetails.mechanic && (
                      <div>
                        <span className="text-gray-400">Mechanic:</span>
                        <div className="text-white">{selectedExerciseForDetails.mechanic}</div>
                      </div>
                    )}
                    {selectedExerciseForDetails.category && (
                      <div>
                        <span className="text-gray-400">Category:</span>
                        <div className="text-white">{selectedExerciseForDetails.category}</div>
                      </div>
                    )}
                    {selectedExerciseForDetails.force && (
                      <div>
                        <span className="text-gray-400">Force Type:</span>
                        <div className="text-white">{selectedExerciseForDetails.force}</div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex gap-2 mt-6">
                  <button
                    onClick={() => {
                      toggleExerciseSelection(selectedExerciseForDetails.id);
                      setSelectedExerciseForDetails(null);
                    }}
                    className={`py-2 px-4 rounded-lg font-medium transition-colors ${
                      selectedExercises.includes(selectedExerciseForDetails.id)
                        ? 'bg-red-600 hover:bg-red-700 text-white'
                        : 'bg-blue-600 hover:bg-blue-700 text-white'
                    }`}
                  >
                    {selectedExercises.includes(selectedExerciseForDetails.id) ? 'Remove' : 'Add To Workout'}
                  </button>
                  <button
                    onClick={() => setSelectedExerciseForDetails(null)}
                    className="flex-2 px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg font-medium transition-colors"
                  >
                    Close
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ExercisesByEquipment;
