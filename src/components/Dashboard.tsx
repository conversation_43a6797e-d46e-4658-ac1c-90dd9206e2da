import React, { useState, useEffect } from 'react';
import { Plus, TrendingUp, Calendar, Target, Weight, Clock, History, ChevronDown, ChevronUp } from 'lucide-react';
import { workoutService } from '../services/workoutService';
import { Workout, MonthlyAnalytics } from '../types';
import { User } from '@supabase/supabase-js';

interface DashboardProps {
  onStartWorkout: () => void;
  onViewHistory: () => void;

  user: User | null;
}

const Dashboard: React.FC<DashboardProps> = ({ onStartWorkout, onViewHistory, user }) => {
  const [recentWorkouts, setRecentWorkouts] = useState<Workout[]>([]);
  const [currentMonthAnalytics, setCurrentMonthAnalytics] = useState<MonthlyAnalytics | null>(null);
  const [loading, setLoading] = useState(true);
  const [isMaxWeightsExpanded, setIsMaxWeightsExpanded] = useState(false);

  useEffect(() => {
    const loadData = async () => {
      if (!user) {
        setLoading(false);
        return;
      }

      try {
        const workouts = await workoutService.getWorkouts();
        setRecentWorkouts(workouts.slice(-5).reverse());
        
        const now = new Date();
        const analytics = await workoutService.getMonthlyAnalytics(now.getMonth(), now.getFullYear());
        setCurrentMonthAnalytics(analytics);
      } catch (error) {
        console.error('Error loading dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

   void loadData();
  }, [user]);

  const formatTime = (time: string) => {
    return new Date(`2000-01-01 ${time}`).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const formatDate = (date: string) => {
    return new Date(date).toLocaleDateString([], { month: 'short', day: 'numeric' });
  };

  const getWorkoutDuration = (workout: Workout) => {
    const start = new Date(`${workout.date} ${workout.startTime}`);
    const end = new Date(`${workout.date} ${workout.endTime}`);
    const duration = (end.getTime() - start.getTime()) / (1000 * 60);
    return Math.round(duration);
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white p-4">
      <div className="max-w-6xl mx-auto">
        {loading && (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          </div>
        )}
        
        {/* Header */}
        <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center mb-8 gap-4">
          <div>
            <h1 className="text-3xl lg:text-4xl font-bold text-white mb-2">My Bodybuilder</h1>
            <p className="text-gray-400">Track your strength journey</p>
          </div>
          <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
            <button
              onClick={onStartWorkout}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 sm:px-6 py-3 rounded-lg flex items-center justify-center gap-2 transition-colors"
            >
              <Plus size={20} />
              <span className="whitespace-nowrap">Start Workout</span>
            </button>

            <button
              onClick={onViewHistory}
              className="bg-gray-600 hover:bg-gray-700 text-white px-4 sm:px-6 py-3 rounded-lg flex items-center justify-center gap-2 transition-colors"
            >
              <History size={20} />
              <span className="whitespace-nowrap">History</span>
            </button>
          </div>
        </div>

        {/* Analytics Cards */}
        {currentMonthAnalytics && (
          <div className="bg-gray-800 rounded-lg p-4 border border-gray-700 mb-6">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
              <Calendar className="text-blue-400" size={20} />
              This Month's Progress
            </h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="bg-blue-600 bg-opacity-20 rounded-lg p-3 mb-2">
                  <Calendar className="text-blue-400 mx-auto mb-1" size={24} />
                  <p className="text-xl font-bold text-white">{currentMonthAnalytics.totalWorkouts}</p>
                </div>
                <p className="text-xs text-gray-400">Workouts</p>
              </div>

              <div className="text-center">
                <div className="bg-green-600 bg-opacity-20 rounded-lg p-3 mb-2">
                  <Target className="text-green-400 mx-auto mb-1" size={24} />
                  <p className="text-xl font-bold text-white">{currentMonthAnalytics.totalSets}</p>
                </div>
                <p className="text-xs text-gray-400">Sets</p>
              </div>

              <div className="text-center">
                <div className="bg-orange-600 bg-opacity-20 rounded-lg p-3 mb-2">
                  <Weight className="text-orange-400 mx-auto mb-1" size={24} />
                  <p className="text-xl font-bold text-white">{currentMonthAnalytics.totalReps}</p>
                </div>
                <p className="text-xs text-gray-400">Reps</p>
              </div>

              <div className="text-center">
                <div className="bg-purple-600 bg-opacity-20 rounded-lg p-3 mb-2">
                  <Clock className="text-purple-400 mx-auto mb-1" size={24} />
                  <p className="text-xl font-bold text-white">{Math.round(currentMonthAnalytics.averageWorkoutDuration)}</p>
                </div>
                <p className="text-xs text-gray-400">Avg Minutes</p>
              </div>
            </div>
          </div>
        )}

        {/* Exercise Max Weights */}
        {currentMonthAnalytics && Object.keys(currentMonthAnalytics.exerciseMaxWeights).length > 0 && (
          <div className="bg-gray-800 rounded-lg p-6 border border-gray-700 mb-8">
            <button
              onClick={() => setIsMaxWeightsExpanded(!isMaxWeightsExpanded)}
              className="w-full flex items-center justify-between text-left mb-4 hover:bg-gray-700 rounded-lg p-2 -m-2 transition-colors"
            >
              <h3 className="text-lg font-semibold text-white flex items-center gap-2">
                <Weight size={20} />
                Exercise Max Weights This Month
                <span className="text-sm text-gray-400 font-normal">
                  ({Object.keys(currentMonthAnalytics.exerciseMaxWeights).length} exercises)
                </span>
              </h3>
              {isMaxWeightsExpanded ? (
                <ChevronUp className="text-gray-400" size={20} />
              ) : (
                <ChevronDown className="text-gray-400" size={20} />
              )}
            </button>
            
            {isMaxWeightsExpanded ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {Object.entries(currentMonthAnalytics.exerciseMaxWeights)
                  .sort(([, a], [, b]) => b - a) // Sort by weight descending
                  .map(([exercise, weight]) => (
                    <div key={exercise} className="bg-gray-700 rounded-lg p-4 hover:bg-gray-600 transition-colors">
                      <h4 className="font-semibold text-white mb-1">{exercise}</h4>
                      <p className="text-2xl font-bold text-blue-400">{weight} kg</p>
                    </div>
                  ))}
              </div>
            ) : (
              <div className="flex flex-wrap gap-2">
                {Object.entries(currentMonthAnalytics.exerciseMaxWeights)
                  .sort(([, a], [, b]) => b - a) // Sort by weight descending
                  .slice(0, 6) // Show only top 6 in collapsed view
                  .map(([exercise, weight]) => (
                    <div key={exercise} className="bg-gray-700 rounded-lg px-3 py-2 hover:bg-gray-600 transition-colors">
                      <span className="text-sm text-gray-300">{exercise}</span>
                      <span className="text-sm font-bold text-blue-400 ml-2">{weight}kg</span>
                    </div>
                  ))}
                {Object.keys(currentMonthAnalytics.exerciseMaxWeights).length > 6 && (
                  <div className="bg-gray-700 rounded-lg px-3 py-2 text-sm text-gray-400">
                    +{Object.keys(currentMonthAnalytics.exerciseMaxWeights).length - 6} more
                  </div>
                )}
              </div>
            )}
          </div>
        )}

        {/* Muscle Groups This Month */}
        {currentMonthAnalytics && Object.keys(currentMonthAnalytics.muscleGroups).length > 0 && (
          <div className="bg-gray-800 rounded-lg p-6 border border-gray-700 mb-8">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
              <TrendingUp size={20} />
              Muscle Groups This Month
            </h3>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
              {Object.entries(currentMonthAnalytics.muscleGroups).map(([muscle, count]) => (
                <div key={muscle} className="text-center">
                  <div className="bg-gray-700 rounded-lg p-3 mb-2">
                    <p className="text-2xl font-bold text-blue-400">{count}</p>
                  </div>
                  <p className="text-sm text-gray-400 capitalize">{muscle}</p>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Recent Workouts */}
        <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
          <h3 className="text-lg font-semibold text-white mb-4">Recent Workouts</h3>
          {recentWorkouts.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-400 mb-4">No workouts yet</p>
              <button
                onClick={onStartWorkout}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
              >
                Start Your First Workout
              </button>
            </div>
          ) : (
            <div className="space-y-4">
              {recentWorkouts.map((workout) => (
                <div key={workout.id} className="bg-gray-700 rounded-lg p-4 hover:bg-gray-600 transition-colors">
                  <div className="flex justify-between items-start">
                    <div>
                      <h4 className="font-semibold text-white capitalize">{workout.muscleGroup} Workout</h4>
                      <p className="text-sm text-gray-400">
                        {formatDate(workout.date)} • {formatTime(workout.startTime)} - {formatTime(workout.endTime)}
                      </p>
                      <p className="text-sm text-gray-400 mt-1">
                        {workout.exercises.length} exercises • {getWorkoutDuration(workout)} minutes
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="text-lg font-bold text-white">{workout.totalSets}</p>
                      <p className="text-sm text-gray-400">sets</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Dashboard;