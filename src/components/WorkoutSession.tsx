import React, { useState, useEffect } from 'react';
import { ArrowLeft, Plus, Trash2, Clock, Check, Weight, Hash, SkipForward, AlertCircle, ChevronDown, ChevronUp, Info } from 'lucide-react';
import { Exercise, ExerciseSession, WorkoutSet, Workout } from '../types';
import { exerciseDataService } from '../services/exerciseDataService';
import { workoutService } from '../services/workoutService';
import { getExerciseImage } from '../utils/exerciseImages';
import { getExerciseSpecificFallback } from '../utils/exerciseImages';

interface WorkoutSessionProps {
  workoutDate: string;
  startTime: string;
  muscleGroup: string; // Now accepts primaryMuscle as string
  exerciseIds: string[];
  onComplete: () => void;
  onBack: () => void;
}

const WorkoutSession: React.FC<WorkoutSessionProps> = ({
  workoutDate,
  startTime,
  muscleGroup,
  exerciseIds,
  onComplete,
  onBack
}) => {
  const [currentExerciseIndex, setCurrentExerciseIndex] = useState(0);
  const [exerciseSessions, setExerciseSessions] = useState<ExerciseSession[]>([]);
  const [currentSets, setCurrentSets] = useState<WorkoutSet[]>([]);
  const [currentSet, setCurrentSet] = useState<WorkoutSet>({ reps: 0, weight: 0 });
  const [exerciseStartTime, setExerciseStartTime] = useState<string>('');
  const [workoutDuration, setWorkoutDuration] = useState<number>(0);
  const [selectedExercises, setSelectedExercises] = useState<Exercise[]>([]);
  const [loading, setLoading] = useState(true);
  const [instructionsExpanded, setInstructionsExpanded] = useState(false);
  const [infoExpanded, setInfoExpanded] = useState(false);
  const [showFinishConfirmation, setShowFinishConfirmation] = useState(false);
  const [showSkipConfirmation, setShowSkipConfirmation] = useState(false);

  const currentExercise = selectedExercises[currentExerciseIndex];

  useEffect(() => {
    const loadExercises = async () => {
      try {
        console.log('🔍 WorkoutSession: Loading exercises for IDs:', exerciseIds);
        const allExercises = await exerciseDataService.loadAllExercises();
        console.log(`📊 WorkoutSession: Found ${allExercises.length} total exercises in database`);
        const filtered = allExercises.filter(ex => exerciseIds.includes(ex.id));
        console.log(`✅ WorkoutSession: Filtered to ${filtered.length} matching exercises:`, filtered.map(ex => ({ id: ex.id, name: ex.name })));
        
        // Load detailed exercise data from JSON files
        const exercisesWithDetails = await Promise.all(filtered.map(async (exercise) => {
          try {
            // Fetch the JSON file for this exercise
            let response = await fetch(`/src/data/exercises/${exercise.name.replace(/ /g, '_')}.json`);
            
            // If the file doesn't exist with the name, try with the ID
            if (!response.ok) {
              const fallbackResponse = await fetch(`/src/data/exercises/${exercise.id}.json`);
              if (fallbackResponse.ok) {
                response = fallbackResponse;
              }
            }
            
            if (response.ok) {
              const detailedData = await response.json();
              return {
                ...exercise,
                instructions: detailedData.instructions || [],
                primaryMuscles: detailedData.primaryMuscles || [],
                secondaryMuscles: detailedData.secondaryMuscles || [],
                equipment: detailedData.equipment || exercise.equipment,
                level: detailedData.level || 'beginner',
                mechanic: detailedData.mechanic || null,
                force: detailedData.force || null,
                category: detailedData.category || 'strength'
              };
            }
            return exercise;
          } catch (err) {
            console.error(`Error loading details for ${exercise.id}:`, err);
            return exercise;
          }
        }));

        setSelectedExercises(exercisesWithDetails);

        console.log(`✅ Loaded ${exercisesWithDetails.length} exercises for workout:`, exercisesWithDetails.map(ex => ex.name));

        if (exercisesWithDetails.length === 0) {
          console.error('❌ No exercises found for the given IDs:', exerciseIds);
          console.log('Available exercises:', allExercises.slice(0, 10).map(ex => ({ id: ex.id, name: ex.name })));
          return;
        }

        // Initialize exercise sessions
        const sessions: ExerciseSession[] = exercisesWithDetails.map((exercise, index) => ({
          id: `session-${exercise.id}-${Date.now()}-${index}`,
          exerciseId: exercise.id,
          exerciseName: exercise.name,
          sets: [],
          startTime: '',
          endTime: '',
          muscleGroup: exercise.primaryMuscles?.[0] || exercise.muscleGroup || muscleGroup,
          maxWeight: 0
        }));

        setExerciseSessions(sessions);
        setExerciseStartTime(new Date().toISOString());
      } catch (error) {
        console.error('Error loading exercises:', error);
      } finally {
        setLoading(false);
      }
    };

    loadExercises();
  }, [exerciseIds]);

  useEffect(() => {
    // Create a proper date object by parsing the date and time separately
    const [year, month, day] = workoutDate.split('-').map(Number);
    const [hours, minutes] = startTime.split(':').map(Number);
    const startDateTime = new Date(year, month - 1, day, hours, minutes, 0);

    console.log('🕐 Timer setup:', { workoutDate, startTime, startDateTime });

    const updateDuration = () => {
      const now = new Date();
      const duration = Math.floor((now.getTime() - startDateTime.getTime()) / 1000);
      console.log('⏱️ Timer update:', { now, startDateTime, duration });
      setWorkoutDuration(Math.max(0, duration)); // Ensure duration is never negative
    };

    const interval = setInterval(updateDuration, 1000);
    updateDuration();

    return () => clearInterval(interval);
  }, [workoutDate, startTime]);

  useEffect(() => {
    setExerciseStartTime(new Date().toTimeString().slice(0, 8));
    setCurrentSets([]);
    setCurrentSet({ reps: 0, weight: 0 });
  }, [currentExerciseIndex]);

  const formatDuration = (seconds: number) => {
    // Ensure we never show negative time
    const positiveSeconds = Math.max(0, seconds);
    const mins = Math.floor(positiveSeconds / 60);
    const secs = positiveSeconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const addSet = () => {
    if (currentSet.reps > 0 && currentSet.weight > 0) {
      setCurrentSets(prev => [...prev, currentSet]);
      setCurrentSet({ reps: 0, weight: 0 });
    }
  };

  const removeSet = (index: number) => {
    setCurrentSets(prev => prev.filter((_, i) => i !== index));
  };

  const completeExercise = async () => {
    if (currentSets.length === 0) {
      alert('Please add at least one set before completing this exercise.');
      return;
    }

    const exerciseSession: ExerciseSession = {
      id: Date.now().toString(),
      exerciseId: currentExercise.id,
      exerciseName: currentExercise.name,
      sets: currentSets,
      startTime: exerciseStartTime,
      endTime: new Date().toTimeString().slice(0, 8),
      muscleGroup,
      maxWeight: Math.max(...currentSets.map(set => set.weight))
    };

    setExerciseSessions(prev => [...prev, exerciseSession]);
    
    if (currentExerciseIndex < selectedExercises.length - 1) {
      setCurrentExerciseIndex(prev => prev + 1);
    } else {
      await completeWorkout([...exerciseSessions, exerciseSession]);
    }
  };

  const skipExercise = () => {
    if (currentExerciseIndex < selectedExercises.length - 1) {
      setCurrentExerciseIndex(prev => prev + 1);
      setShowSkipConfirmation(false);
    } else {
      // If this is the last exercise, show finish confirmation
      setShowFinishConfirmation(true);
      setShowSkipConfirmation(false);
    }
  };

  const finishWorkoutEarly = async () => {
    if (exerciseSessions.length === 0) {
      alert('You need to complete at least one exercise to save the workout.');
      return;
    }
    await completeWorkout(exerciseSessions);
  };

  const completeWorkout = async (allSessions: ExerciseSession[]) => {
    const totalSets = allSessions.reduce((sum, session) => sum + session.sets.length, 0);
    const totalReps = allSessions.reduce((sum, session) => 
      sum + session.sets.reduce((setSum, set) => setSum + set.reps, 0), 0
    );

    const workout: Workout = {
      id: Date.now().toString(),
      date: workoutDate,
      startTime,
      endTime: new Date().toTimeString().slice(0, 5),
      muscleGroup,
      exercises: allSessions,
      totalSets,
      totalReps
    };

    try {
      await workoutService.saveWorkout(workout);
    } catch (error) {
      console.error('Error saving workout:', error);
    }
    onComplete();
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900 text-white p-4 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading workout...</p>
        </div>
      </div>
    );
  }

  if (!currentExercise) {
    return (
      <div className="min-h-screen bg-gray-900 text-white p-4 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-400">No exercises found</p>
          <button
            onClick={onBack}
            className="mt-4 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900 text-white p-4">
      <div className="max-w-2xl mx-auto">
        <div className="flex items-center justify-between mb-6">
          <button
            onClick={onBack}
            className="text-gray-400 hover:text-white transition-colors"
          >
            <ArrowLeft size={24} />
          </button>
          
          <div className="flex items-center gap-2 text-green-400">
            <Clock size={20} />
            <span className="font-mono text-lg">{formatDuration(workoutDuration)}</span>
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg p-4 border border-gray-700 mb-6">
          {/* Exercise Header */}
          <div className="flex items-center justify-between mb-3">
            <div>
              <h2 className="text-lg font-semibold text-white mb-1">{currentExercise.name}</h2>
              <div className="flex items-center gap-3 text-sm text-gray-400">
                <span className="capitalize">{currentExercise.equipment}</span>
                <span>•</span>
                <span>Target: {currentExercise.muscleGroup}</span>
              </div>
            </div>
            <span className="text-sm text-gray-400 bg-gray-700 px-3 py-1 rounded-full">
              {currentExerciseIndex + 1} / {selectedExercises.length}
            </span>
          </div>

          {/* Exercise Image */}
          <div className="mb-4 flex justify-center">
            <div className="relative w-full max-w-sm h-72 rounded-lg overflow-hidden bg-gray-700 flex items-center justify-center">
              <img
                src={getExerciseImage(currentExercise.id)}
                alt={`${currentExercise.name} demonstration`}
                className="max-w-full max-h-full object-contain"
                onError={(e) => {
                  const target = e.currentTarget as HTMLImageElement;
                  target.src = getExerciseSpecificFallback(currentExercise.id, currentExercise.muscleGroup, currentExercise.equipment);
                }}
              />
            </div>
          </div>

          <div className="flex items-center justify-between text-sm text-gray-400 mb-4">
            <span>{currentSets.length} sets completed</span>
            {currentExercise.level && (
              <span className="bg-gray-700 text-gray-300 px-2 py-1 rounded text-xs">
                {currentExercise.level}
              </span>
            )}
          </div>
          
          <div className="exercise-details mb-6 bg-gray-800 rounded-lg overflow-hidden">
             {currentExercise.instructions && currentExercise.instructions.length > 0 && (
               <div className="border-b border-gray-700">
                 <button
                   onClick={() => setInstructionsExpanded(!instructionsExpanded)}
                   className="w-full p-4 text-left hover:bg-gray-700 transition-colors"
                 >
                   <h4 className="text-white font-medium flex items-center justify-between">
                     <div className="flex items-center">
                       <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                         <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                       </svg>
                       <span>Instructions</span>
                       {!instructionsExpanded && (
                         <span className="ml-2 text-xs text-gray-500">({currentExercise.instructions.length} steps)</span>
                       )}
                     </div>
                     {instructionsExpanded ? (
                       <ChevronUp size={20} className="text-gray-400 hover:text-white transition-colors" />
                     ) : (
                       <ChevronDown size={20} className="text-gray-400 hover:text-white transition-colors" />
                     )}
                   </h4>
                 </button>
                 <div className={`transition-all duration-300 ease-in-out overflow-hidden ${
                   instructionsExpanded ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
                 }`}>
                   <div className="px-4 pb-4">
                     <ol className="list-decimal pl-5 text-gray-300 text-sm space-y-1">
                       {currentExercise.instructions.map((instruction, idx) => (
                         <li key={idx}>{instruction}</li>
                       ))}
                     </ol>
                   </div>
                 </div>
               </div>
             )}

             {/* Info Section - Collapsible */}
             <div className="border-b border-gray-700">
               <button
                 onClick={() => setInfoExpanded(!infoExpanded)}
                 className="w-full p-4 text-left hover:bg-gray-700 transition-colors"
               >
                 <h4 className="text-white font-medium flex items-center justify-between">
                   <div className="flex items-center">
                     <Info size={20} className="mr-2" />
                     <span>Info</span>
                     {!infoExpanded && (
                       <span className="ml-2 text-xs text-gray-500">(exercise details)</span>
                     )}
                   </div>
                   {infoExpanded ? (
                     <ChevronUp size={20} className="text-gray-400 hover:text-white transition-colors" />
                   ) : (
                     <ChevronDown size={20} className="text-gray-400 hover:text-white transition-colors" />
                   )}
                 </h4>
               </button>
               <div className={`transition-all duration-300 ease-in-out overflow-hidden ${
                 infoExpanded ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
               }`}>
                 <div className="px-4 pb-4">
                   <div className="grid grid-cols-2 gap-4 text-sm">
                     {currentExercise.primaryMuscles && currentExercise.primaryMuscles.length > 0 && (
                       <div className="col-span-2 md:col-span-1">
                         <h5 className="text-gray-400 font-medium mb-1">Primary Muscles</h5>
                         <p className="text-white capitalize">{currentExercise.primaryMuscles.join(', ')}</p>
                       </div>
                     )}

                     {currentExercise.secondaryMuscles && currentExercise.secondaryMuscles.length > 0 && (
                       <div className="col-span-2 md:col-span-1">
                         <h5 className="text-gray-400 font-medium mb-1">Secondary Muscles</h5>
                         <p className="text-white capitalize">{currentExercise.secondaryMuscles.join(', ')}</p>
                       </div>
                     )}

                     {currentExercise.level && (
                       <div>
                         <h5 className="text-gray-400 font-medium mb-1">Level</h5>
                         <p className="text-white capitalize">{currentExercise.level}</p>
                       </div>
                     )}

                     {currentExercise.mechanic && (
                       <div>
                         <h5 className="text-gray-400 font-medium mb-1">Mechanic</h5>
                         <p className="text-white capitalize">{currentExercise.mechanic}</p>
                       </div>
                     )}

                     {currentExercise.force && (
                       <div>
                         <h5 className="text-gray-400 font-medium mb-1">Force</h5>
                         <p className="text-white capitalize">{currentExercise.force}</p>
                       </div>
                     )}

                     {currentExercise.category && (
                       <div>
                         <h5 className="text-gray-400 font-medium mb-1">Category</h5>
                         <p className="text-white capitalize">{currentExercise.category}</p>
                       </div>
                     )}
                   </div>
                 </div>
               </div>
             </div>
           </div>

          <div className="grid grid-cols-2 gap-4 mb-4">
            <div>
              <label htmlFor="reps-input" className="block text-sm font-medium text-gray-300 mb-2">
                <Hash size={16} className="inline mr-1" />
                Reps
              </label>
              <input
                id="reps-input"
                type="number"
                value={currentSet.reps || ''}
                onChange={(e) => setCurrentSet(prev => ({ ...prev, reps: parseInt(e.target.value) || 0 }))}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:border-blue-500 focus:outline-none"
                placeholder="0"
              />
            </div>
            
            <div>
              <label htmlFor="weight-input" className="block text-sm font-medium text-gray-300 mb-2">
                <Weight size={16} className="inline mr-1" />
                Weight (kg)
              </label>
              <input
                id="weight-input"
                type="number"
                value={currentSet.weight || ''}
                onChange={(e) => setCurrentSet(prev => ({ ...prev, weight: parseInt(e.target.value) || 0 }))}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:border-blue-500 focus:outline-none"
                placeholder="0"
              />
            </div>
          </div>

          <button
            onClick={addSet}
            disabled={currentSet.reps === 0 || currentSet.weight === 0}
            className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white py-2 rounded-lg font-semibold transition-colors flex items-center justify-center gap-2 mb-6"
          >
            <Plus size={20} />
            Add Set
          </button>

          {currentSets.length > 0 && (
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-white mb-3">Sets</h3>
              <div className="space-y-2">
                {currentSets.map((set, index) => (
                  <div key={index} className="flex items-center justify-between bg-gray-700 rounded-lg p-3">
                    <span className="text-white">
                      Set {index + 1}: {set.reps} reps × {set.weight} kg
                    </span>
                    <button
                      onClick={() => removeSet(index)}
                      className="text-red-400 hover:text-red-300 transition-colors"
                    >
                      <Trash2 size={16} />
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}

          <button
            onClick={completeExercise}
            className="w-full bg-green-600 hover:bg-green-700 text-white py-3 rounded-lg font-semibold transition-colors flex items-center justify-center gap-2 mb-3"
          >
            <Check size={20} />
            {currentExerciseIndex < selectedExercises.length - 1 ? 'Complete Exercise' : 'Complete Workout'}
          </button>

          <div className="flex gap-3">
            <button
              onClick={() => setShowSkipConfirmation(true)}
              className="flex-1 bg-gray-600 hover:bg-gray-700 text-white py-2 rounded-lg font-medium transition-colors flex items-center justify-center gap-2"
            >
              <SkipForward size={16} />
              Skip Exercise
            </button>
            
            {exerciseSessions.length > 0 && (
              <button
                onClick={() => setShowFinishConfirmation(true)}
                className="flex-1 bg-orange-600 hover:bg-orange-700 text-white py-2 rounded-lg font-medium transition-colors flex items-center justify-center gap-2"
              >
                <AlertCircle size={16} />
                Finish Early
              </button>
            )}
          </div>
        </div>

        {exerciseSessions.filter(session => session.sets.length > 0).length > 0 && (
          <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
            <h3 className="font-semibold text-white mb-3">Completed Exercises</h3>
            <div className="space-y-2">
              {exerciseSessions.filter(session => session.sets.length > 0).map((session) => (
                <div key={session.id} className="flex items-center justify-between text-sm">
                  <span className="text-gray-300">{session.exerciseName}</span>
                  <span className="text-gray-400">{session.sets.length} sets • Max: {session.maxWeight} kg</span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Skip Exercise Confirmation Modal */}
        {showSkipConfirmation && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-gray-800 rounded-lg p-6 w-full max-w-md border border-gray-700">
              <div className="flex items-center gap-3 mb-4">
                <SkipForward className="text-yellow-400" size={24} />
                <h3 className="text-lg font-semibold text-white">Skip Exercise?</h3>
              </div>
              
              <p className="text-gray-300 mb-6">
                Are you sure you want to skip "{currentExercise.name}"? 
                {currentSets.length > 0 && " Any sets you've added will be lost."}
              </p>
              
              <div className="flex gap-3">
                <button
                  onClick={() => setShowSkipConfirmation(false)}
                  className="flex-1 bg-gray-600 hover:bg-gray-700 text-white py-2 rounded-lg transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={skipExercise}
                  className="flex-1 bg-yellow-600 hover:bg-yellow-700 text-white py-2 rounded-lg transition-colors"
                >
                  Skip Exercise
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Finish Workout Early Confirmation Modal */}
        {showFinishConfirmation && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-gray-800 rounded-lg p-6 w-full max-w-md border border-gray-700">
              <div className="flex items-center gap-3 mb-4">
                <AlertCircle className="text-orange-400" size={24} />
                <h3 className="text-lg font-semibold text-white">Finish Workout Early?</h3>
              </div>
              
              <div className="mb-6">
                <p className="text-gray-300 mb-3">
                  You've completed {exerciseSessions.length} out of {selectedExercises.length} exercises.
                </p>
                <p className="text-gray-300">
                  Are you sure you want to finish your workout now? This will save your progress.
                </p>
              </div>
              
              <div className="flex gap-3">
                <button
                  onClick={() => setShowFinishConfirmation(false)}
                  className="flex-1 bg-gray-600 hover:bg-gray-700 text-white py-2 rounded-lg transition-colors"
                >
                  Continue Workout
                </button>
                <button
                  onClick={finishWorkoutEarly}
                  className="flex-1 bg-orange-600 hover:bg-orange-700 text-white py-2 rounded-lg transition-colors"
                >
                  Finish Workout
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default WorkoutSession;