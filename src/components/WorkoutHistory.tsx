import React, {useCallback, useEffect, useState} from 'react';
import {ArrowLeft, Calendar, Clock, Edit2, Hash, Plus, Save, Trash2, Weight, X} from 'lucide-react';
import {workoutService} from '../services/workoutService';
import {exerciseService} from '../services/exerciseService';
import {Exercise, Workout, WorkoutSet} from '../types';
import {getImageFromData, getExerciseSpecificFallback} from '../utils/exerciseImages';

interface WorkoutHistoryProps {
  onBack: () => void;
}

const WorkoutHistory: React.FC<WorkoutHistoryProps> = ({ onBack }) => {
  const [workouts, setWorkouts] = useState<Workout[]>([]);
  const [loading, setLoading] = useState(true);
  const [editingWorkout, setEditingWorkout] = useState<Workout | null>(null);
  const [editingExerciseIndex, setEditingExerciseIndex] = useState<number | null>(null);
  const [availableExercises, setAvailableExercises] = useState<Exercise[]>([]);
  const [newSet, setNewSet] = useState<WorkoutSet>({ reps: 0, weight: 0 });

  useEffect(() => {
    void loadWorkouts();
    void loadExercises();
  }, []);

  const loadWorkouts = useCallback(async () => {
    try {
      const data = await workoutService.getWorkouts();
      setWorkouts(data);
    } catch (error) {
      console.error('Error loading workouts:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  const loadExercises = useCallback(async () => {
    try {
      const data = await exerciseService.getAllExercises();
      setAvailableExercises(data);
    } catch (error) {
      console.error('Error loading exercises:', error);
    }
  }, []);

  const startEditingWorkout = (workout: Workout) => {
    setEditingWorkout({ ...workout });
    setEditingExerciseIndex(null);
  };

  const cancelEditing = () => {
    setEditingWorkout(null);
    setEditingExerciseIndex(null);
    setNewSet({ reps: 0, weight: 0 });
  };

  const saveWorkout = async () => {
    if (!editingWorkout) return;

    try {
      // Calculate totals
      const totalSets = editingWorkout.exercises.reduce((sum, ex) => sum + ex.sets.length, 0);
      const totalReps = editingWorkout.exercises.reduce((sum, ex) =>
        sum + ex.sets.reduce((setSum, set) => setSum + set.reps, 0), 0
      );

      const updatedWorkout = {
        ...editingWorkout,
        totalSets,
        totalReps
      };

      // Use the update method instead of delete/recreate
      await workoutService.updateWorkout(editingWorkout.id, updatedWorkout);

      await loadWorkouts();
      setEditingWorkout(null);
      setEditingExerciseIndex(null);
    } catch (error) {
      console.error('Error saving workout:', error);
    }
  };

  const deleteWorkout = async (workoutId: string) => {
    if (confirm('Are you sure you want to delete this workout?')) {
      try {
        await workoutService.deleteWorkout(workoutId);
        await loadWorkouts();
      } catch (error) {
        console.error('Error deleting workout:', error);
      }
    }
  };

  const addSetToExercise = () => {
    if (!editingWorkout || editingExerciseIndex === null || newSet.reps === 0 || newSet.weight === 0) return;

    const updatedWorkout = { ...editingWorkout };
    updatedWorkout.exercises[editingExerciseIndex].sets.push({ ...newSet });

    // Update max weight
    updatedWorkout.exercises[editingExerciseIndex].maxWeight = Math.max(...updatedWorkout.exercises[editingExerciseIndex].sets.map(set => set.weight));

    setEditingWorkout(updatedWorkout);
    setNewSet({ reps: 0, weight: 0 });
  };

  const removeSetFromExercise = (setIndex: number) => {
    if (!editingWorkout || editingExerciseIndex === null) return;

    const updatedWorkout = { ...editingWorkout };
    updatedWorkout.exercises[editingExerciseIndex].sets.splice(setIndex, 1);

    // Update max weight
    const sets = updatedWorkout.exercises[editingExerciseIndex].sets;
    updatedWorkout.exercises[editingExerciseIndex].maxWeight = sets.length > 0 ? Math.max(...sets.map(set => set.weight)) : 0;

    setEditingWorkout(updatedWorkout);
  };

  const updateSet = (setIndex: number, field: 'reps' | 'weight', value: number) => {
    if (!editingWorkout || editingExerciseIndex === null) return;

    const updatedWorkout = { ...editingWorkout };
    updatedWorkout.exercises[editingExerciseIndex].sets[setIndex][field] = value;

    // Update max weight if weight was changed
    if (field === 'weight') {
      updatedWorkout.exercises[editingExerciseIndex].maxWeight = Math.max(...updatedWorkout.exercises[editingExerciseIndex].sets.map(set => set.weight));
    }

    setEditingWorkout(updatedWorkout);
  };

  const removeExercise = (exerciseIndex: number) => {
    if (!editingWorkout) return;

    const updatedWorkout = { ...editingWorkout };
    updatedWorkout.exercises.splice(exerciseIndex, 1);
    setEditingWorkout(updatedWorkout);
    setEditingExerciseIndex(null);
  };

  const formatTime = (time: string) => {
    return new Date(`2000-01-01 ${time}`).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const formatDate = (date: string) => {
    return new Date(date).toLocaleDateString([], {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getWorkoutDuration = (workout: Workout) => {
    const start = new Date(`${workout.date} ${workout.startTime}`);
    const end = new Date(`${workout.date} ${workout.endTime}`);
    const duration = (end.getTime() - start.getTime()) / (1000 * 60);
    return Math.round(duration);
  };




  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900 text-white p-4 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading workout history...</p>
        </div>
      </div>
    );
  }

  if (editingWorkout) {
    return (
      <div className="min-h-screen bg-gray-900 text-white p-4">
        <div className="max-w-4xl mx-auto">
          <div className="flex items-center justify-between mb-6">
            <button
              onClick={cancelEditing}
              className="text-gray-400 hover:text-white transition-colors"
            >
              <ArrowLeft size={24} />
            </button>
            <h1 className="text-2xl font-bold text-white">Edit Workout</h1>
            <div className="flex gap-2">
              <button
                onClick={saveWorkout}
                className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
              >
                <Save size={16} />
                Save
              </button>
              <button
                onClick={cancelEditing}
                className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
              >
                <X size={16} />
                Cancel
              </button>
            </div>
          </div>

          <div className="bg-gray-800 rounded-lg p-6 border border-gray-700 mb-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Date</label>
                <input
                  type="date"
                  value={editingWorkout.date}
                  onChange={(e) => setEditingWorkout(prev => prev ? { ...prev, date: e.target.value } : null)}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:border-blue-500 focus:outline-none"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Start Time</label>
                <input
                  type="time"
                  value={editingWorkout.startTime}
                  onChange={(e) => setEditingWorkout(prev => prev ? { ...prev, startTime: e.target.value } : null)}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:border-blue-500 focus:outline-none"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">End Time</label>
                <input
                  type="time"
                  value={editingWorkout.endTime}
                  onChange={(e) => setEditingWorkout(prev => prev ? { ...prev, endTime: e.target.value } : null)}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:border-blue-500 focus:outline-none"
                />
              </div>
            </div>
            <div className="text-sm text-gray-400">
              <span className="capitalize">{editingWorkout.muscleGroup}</span> •
              <span className="ml-2">{editingWorkout.exercises.length} exercises</span> •
              <span className="ml-2">{getWorkoutDuration(editingWorkout)} minutes</span>
            </div>
          </div>

          <div className="space-y-4">
            {editingWorkout.exercises.map((exercise, exerciseIndex) => (
              <div key={exercise.id} className="bg-gray-800 rounded-lg p-6 border border-gray-700">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-white">{exercise.exerciseName}</h3>
                  <div className="flex gap-2">
                    <button
                      onClick={() => setEditingExerciseIndex(editingExerciseIndex === exerciseIndex ? null : exerciseIndex)}
                      className="text-blue-400 hover:text-blue-300 transition-colors"
                    >
                      <Edit2 size={16} />
                    </button>
                    <button
                      onClick={() => removeExercise(exerciseIndex)}
                      className="text-red-400 hover:text-red-300 transition-colors"
                    >
                      <Trash2 size={16} />
                    </button>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">Start Time</label>
                    <input
                      type="time"
                      value={exercise.startTime}
                      onChange={(e) => {
                        const updatedWorkout = { ...editingWorkout };
                        updatedWorkout.exercises[exerciseIndex].startTime = e.target.value;
                        setEditingWorkout(updatedWorkout);
                      }}
                      className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:border-blue-500 focus:outline-none"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">End Time</label>
                    <input
                      type="time"
                      value={exercise.endTime}
                      onChange={(e) => {
                        const updatedWorkout = { ...editingWorkout };
                        updatedWorkout.exercises[exerciseIndex].endTime = e.target.value;
                        setEditingWorkout(updatedWorkout);
                      }}
                      className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:border-blue-500 focus:outline-none"
                    />
                  </div>
                </div>

                <div className="space-y-2 mb-4">
                  <h4 className="text-sm font-medium text-gray-300">Sets ({exercise.sets.length})</h4>
                  {exercise.sets.map((set, setIndex) => (
                    <div key={setIndex} className="flex items-center gap-4 bg-gray-700 rounded-lg p-3">
                      <span className="text-sm text-gray-400 w-12">Set {setIndex + 1}</span>
                      {editingExerciseIndex === exerciseIndex ? (
                        <>
                          <div className="flex items-center gap-2">
                            <Hash size={16} className="text-gray-400" />
                            <input
                              type="number"
                              value={set.reps}
                              onChange={(e) => updateSet(setIndex, 'reps', parseInt(e.target.value) || 0)}
                              className="w-16 bg-gray-600 border border-gray-500 rounded px-2 py-1 text-white text-sm focus:border-blue-500 focus:outline-none"
                            />
                            <span className="text-xs text-gray-400">reps</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Weight size={16} className="text-gray-400" />
                            <input
                              type="number"
                              value={set.weight}
                              onChange={(e) => updateSet(setIndex, 'weight', parseInt(e.target.value) || 0)}
                              className="w-16 bg-gray-600 border border-gray-500 rounded px-2 py-1 text-white text-sm focus:border-blue-500 focus:outline-none"
                            />
                            <span className="text-xs text-gray-400">kg</span>
                          </div>
                          <button
                            onClick={() => removeSetFromExercise(setIndex)}
                            className="text-red-400 hover:text-red-300 transition-colors"
                          >
                            <Trash2 size={14} />
                          </button>
                        </>
                      ) : (
                        <span className="text-white">{set.reps} reps × {set.weight} kg</span>
                      )}
                    </div>
                  ))}
                </div>

                {editingExerciseIndex === exerciseIndex && (
                  <div className="border-t border-gray-600 pt-4">
                    <h4 className="text-sm font-medium text-gray-300 mb-3">Add New Set</h4>
                    <div className="flex items-center gap-4 mb-3">
                      <div className="flex items-center gap-2">
                        <Hash size={16} className="text-gray-400" />
                        <input
                          type="number"
                          value={newSet.reps || ''}
                          onChange={(e) => setNewSet(prev => ({ ...prev, reps: parseInt(e.target.value) || 0 }))}
                          className="w-16 bg-gray-700 border border-gray-600 rounded px-2 py-1 text-white text-sm focus:border-blue-500 focus:outline-none"
                          placeholder="0"
                        />
                        <span className="text-xs text-gray-400">reps</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Weight size={16} className="text-gray-400" />
                        <input
                          type="number"
                          value={newSet.weight || ''}
                          onChange={(e) => setNewSet(prev => ({ ...prev, weight: parseInt(e.target.value) || 0 }))}
                          className="w-16 bg-gray-700 border border-gray-600 rounded px-2 py-1 text-white text-sm focus:border-blue-500 focus:outline-none"
                          placeholder="0"
                        />
                        <span className="text-xs text-gray-400">kg</span>
                      </div>
                      <button
                        onClick={addSetToExercise}
                        disabled={newSet.reps === 0 || newSet.weight === 0}
                        className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white px-3 py-1 rounded text-sm flex items-center gap-1 transition-colors"
                      >
                        <Plus size={14} />
                        Add Set
                      </button>
                    </div>
                  </div>
                )}

                <div className="text-sm text-gray-400">
                  Max weight: {exercise.maxWeight} kg • {exercise.sets.length} sets
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900 text-white p-4">
      <div className="max-w-6xl mx-auto">
        <div className="flex items-center mb-8">
          <button
            onClick={onBack}
            className="text-gray-400 hover:text-white mr-4 transition-colors"
          >
            <ArrowLeft size={24} />
          </button>
          <h1 className="text-3xl font-bold text-white">Workout History</h1>
        </div>

        {workouts.length === 0 ? (
          <div className="text-center py-12">
            <Calendar className="text-gray-600 mx-auto mb-4" size={48} />
            <h2 className="text-xl font-semibold text-gray-400 mb-2">No workouts yet</h2>
            <p className="text-gray-500 mb-6">Start your first workout to see it here</p>
            <button
              onClick={onBack}
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors"
            >
              Start Your First Workout
            </button>
          </div>
        ) : (
          <div className="space-y-6">
            {workouts.map((workout) => (
              <div key={workout.id} className="bg-gray-800 rounded-lg p-6 border border-gray-700">
                <div className="flex items-start justify-between mb-4">
                  <div>
                    <h3 className="text-xl font-semibold text-white capitalize mb-1">
                      {workout.muscleGroup} Workout
                    </h3>
                    <p className="text-gray-400 mb-2">
                      {formatDate(workout.date)}
                    </p>
                    <div className="flex items-center gap-4 text-sm text-gray-400">
                      <div className="flex items-center gap-1">
                        <Clock size={16} />
                        {formatTime(workout.startTime)} - {formatTime(workout.endTime)}
                      </div>
                      <span>•</span>
                      <span>{getWorkoutDuration(workout)} minutes</span>
                      <span>•</span>
                      <span>{workout.exercises.length} exercises</span>
                      <span>•</span>
                      <span>{workout.totalSets} sets</span>
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <button
                      onClick={() => startEditingWorkout(workout)}
                      className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded-lg flex items-center gap-2 transition-colors"
                    >
                      <Edit2 size={16} />
                      Edit
                    </button>
                    <button
                      onClick={() => deleteWorkout(workout.id)}
                      className="bg-red-600 hover:bg-red-700 text-white px-3 py-2 rounded-lg flex items-center gap-2 transition-colors"
                    >
                      <Trash2 size={16} />
                      Delete
                    </button>
                  </div>
                </div>

                <div className="space-y-3">
                  {workout.exercises.map((exercise) => (
                    <div key={exercise.id} className="bg-gray-700 rounded-lg p-4">
                      <div className="flex items-start gap-3 mb-3">
                        <div className="relative w-16 h-16 rounded-lg overflow-hidden bg-gray-600 flex-shrink-0">
                          <img
                            src={getImageFromData(exercise.exerciseId || exercise.exerciseName)}
                            alt={exercise.exerciseName}
                            className="w-full h-full object-cover"
                            onError={(e) => {
                              const target = e.currentTarget as HTMLImageElement;
                              target.src = getExerciseSpecificFallback(exercise.exerciseId || exercise.exerciseName, exercise.muscleGroup, 'dumbbell');
                            }}
                          />
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="font-medium text-white">{exercise.exerciseName}</h4>
                            <span className="text-sm text-gray-400">
                              Max: {exercise.maxWeight} kg
                            </span>
                          </div>
                          <div className="flex flex-wrap gap-2">
                            {exercise.sets.map((set, setIndex) => (
                              <span
                                key={setIndex}
                                className="bg-gray-600 text-gray-300 px-2 py-1 rounded text-sm"
                              >
                                {set.reps} × {set.weight}kg
                              </span>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default WorkoutHistory;