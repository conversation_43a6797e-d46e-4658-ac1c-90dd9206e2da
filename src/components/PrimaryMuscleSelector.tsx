import React, { useState, useEffect } from 'react';
import { ArrowLeft, Search, Target, Zap, Shield, Activity, Heart, Dumbbell } from 'lucide-react';
import { exerciseDataService } from '../services/exerciseDataService';

interface PrimaryMuscleSelectorProps {
  onBack: () => void;
  onPrimaryMuscleSelected: (primaryMuscle: string) => void;
}

// Define muscle group categories
const MUSCLE_GROUPS = {
  'Legs': {
    muscles: ['abductors', 'adductors', 'quadriceps', 'hamstrings', 'calves', 'glutes'],
    icon: 'legs',
    color: 'from-blue-500 to-blue-700'
  },
  'Arms': {
    muscles: ['biceps', 'triceps', 'forearms'],
    icon: 'arms',
    color: 'from-red-500 to-red-700'
  },
  'Shoulders': {
    muscles: ['shoulders', 'traps'],
    icon: 'shoulders',
    color: 'from-yellow-500 to-yellow-700'
  },
  'Back': {
    muscles: ['middle back', 'lats', 'lower back'],
    icon: 'back',
    color: 'from-green-500 to-green-700'
  },
  'Chest': {
    muscles: ['chest'],
    icon: 'chest',
    color: 'from-purple-500 to-purple-700'
  },
  'Core': {
    muscles: ['abdominals'],
    icon: 'core',
    color: 'from-orange-500 to-orange-700'
  }
};

const PrimaryMuscleSelector: React.FC<PrimaryMuscleSelectorProps> = ({ onBack, onPrimaryMuscleSelected }) => {
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [muscleGroups, setMuscleGroups] = useState<Array<{name: string, muscles: string[], totalExercises: number}>>([]);
  const [allMuscleData, setAllMuscleData] = useState<Record<string, number>>({});

  useEffect(() => {
    loadPrimaryMuscles();
  }, []);

  const loadPrimaryMuscles = async () => {
    try {
      setLoading(true);

      // Load all exercises grouped by primary muscle and equipment
      const grouped = await exerciseDataService.getExercisesGroupedByPrimaryMuscleAndEquipment();

      // Filter out neck exercises completely
      const filteredGrouped = Object.fromEntries(
        Object.entries(grouped).filter(([muscle]) =>
          !muscle.toLowerCase().includes('neck')
        )
      );

      // Calculate exercise counts for each individual muscle
      const muscleCounts: Record<string, number> = {};
      Object.entries(filteredGrouped).forEach(([muscle, equipmentGroups]) => {
        muscleCounts[muscle] = Object.values(equipmentGroups).reduce((total, exercises) => total + exercises.length, 0);
      });
      setAllMuscleData(muscleCounts);

      // Create grouped muscle categories
      const groupedMuscles = Object.entries(MUSCLE_GROUPS).map(([groupName, groupData]) => {
        const totalExercises = groupData.muscles.reduce((total, muscle) => {
          return total + (muscleCounts[muscle] || 0);
        }, 0);

        return {
          name: groupName,
          muscles: groupData.muscles.filter(muscle => muscleCounts[muscle] > 0), // Only include muscles with exercises
          totalExercises
        };
      }).filter(group => group.totalExercises > 0); // Only include groups with exercises

      setMuscleGroups(groupedMuscles);

      console.log(`✅ Created ${groupedMuscles.length} muscle group categories:`, groupedMuscles);
    } catch (error) {
      console.error('Error loading primary muscles:', error);
    } finally {
      setLoading(false);
    }
  };

  const getMuscleGroupIcon = (groupName: string) => {
    const groupIcons: Record<string, React.ReactNode> = {
      'Legs': <Target size={24} className="text-white" />,
      'Arms': <Dumbbell size={24} className="text-white" />,
      'Shoulders': <Target size={24} className="text-white" />,
      'Back': <Shield size={24} className="text-white" />,
      'Chest': <Shield size={24} className="text-white" />,
      'Core': <Zap size={24} className="text-white" />,
    };

    return groupIcons[groupName] || <Activity size={24} className="text-white" />;
  };

  const getMuscleGroupColor = (groupName: string) => {
    return MUSCLE_GROUPS[groupName as keyof typeof MUSCLE_GROUPS]?.color || 'from-gray-500 to-gray-700';
  };

  // Filter muscle groups based on search term
  const filteredMuscleGroups = muscleGroups.filter(group =>
    group.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    group.muscles.some(muscle => muscle.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // Handle muscle group selection - pass the group name which will be used to get all muscles in the group
  const handleMuscleGroupSelection = (groupName: string) => {
    const group = muscleGroups.find(g => g.name === groupName);
    if (group && group.muscles.length > 0) {
      // Pass the group name, and we'll modify ExercisesByEquipment to handle muscle groups
      onPrimaryMuscleSelected(groupName);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900 text-white p-4 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading muscle groups...</p>
          <p className="text-sm text-gray-500 mt-2">Reading exercise database...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900 text-white p-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex items-center mb-8">
          <button
            onClick={onBack}
            className="text-gray-400 hover:text-white mr-4 transition-colors"
          >
            <ArrowLeft size={24} />
          </button>
          <h1 className="text-2xl sm:text-3xl font-bold text-white">Choose Primary Muscle Group</h1>
        </div>

        {/* Search */}
        <div className="mb-6">
          <div className="relative">
            <Search className="absolute left-3 top-3 text-gray-400" size={20} />
            <input
              type="text"
              placeholder="Search muscle groups..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full bg-gray-800 border border-gray-700 rounded-lg pl-10 pr-4 py-3 text-white focus:border-blue-500 focus:outline-none"
            />
          </div>
        </div>

        {/* Statistics */}
        <div className="mb-8 bg-gray-800 rounded-lg p-4 border border-gray-700">
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-blue-400">{muscleGroups.length}</div>
              <div className="text-sm text-gray-400">Muscle Group Categories</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-green-400">
                {Object.values(allMuscleData).reduce((total, count) => total + count, 0)}
              </div>
              <div className="text-sm text-gray-400">Total Exercises</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-purple-400">{filteredMuscleGroups.length}</div>
              <div className="text-sm text-gray-400">Filtered Results</div>
            </div>
          </div>
        </div>

        {/* Muscle Group Categories Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredMuscleGroups.map((group) => (
            <button
              key={group.name}
              onClick={() => handleMuscleGroupSelection(group.name)}
              className="group relative bg-gray-800 hover:bg-gray-700 border border-gray-700 rounded-lg p-6 transition-all duration-200 hover:scale-105"
            >
              <div className={`absolute inset-0 bg-gradient-to-r ${getMuscleGroupColor(group.name)} opacity-0 group-hover:opacity-10 rounded-lg transition-opacity`} />

              <div className="relative">
                <div className={`w-16 h-16 bg-gradient-to-r ${getMuscleGroupColor(group.name)} rounded-lg flex items-center justify-center mb-4 mx-auto`}>
                  {getMuscleGroupIcon(group.name)}
                </div>

                <h3 className="text-xl font-semibold text-white mb-2">{group.name}</h3>
                <p className="text-gray-400 text-sm mb-2">
                  {group.totalExercises} exercise{group.totalExercises !== 1 ? 's' : ''} available
                </p>
                <p className="text-gray-500 text-xs mb-4">
                  {group.muscles.map(muscle => muscle.charAt(0).toUpperCase() + muscle.slice(1)).join(', ')}
                </p>

                <div className="flex items-center justify-center text-blue-400 group-hover:text-blue-300 transition-colors">
                  <span className="text-sm font-medium">View Exercises</span>
                  <Target size={16} className="ml-1" />
                </div>
              </div>
            </button>
          ))}
        </div>

        {filteredMuscleGroups.length === 0 && (
          <div className="text-center py-12">
            <div className="text-gray-400 mb-4">
              <Search size={48} className="mx-auto mb-4 opacity-50" />
              <p className="text-lg">No muscle groups found</p>
              <p className="text-sm">Try adjusting your search terms</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PrimaryMuscleSelector;
