import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

// Create a mock client if Supa<PERSON> is not configured
let supabase: any;

if (!supabaseUrl || !supabaseAnonKey) {
  console.warn('Supabase environment variables not found. Running in offline mode.');
  
  // Create a mock Supabase client for offline functionality
  supabase = {
    auth: {
      getUser: () => Promise.resolve({ data: { user: null }, error: null }),
      getSession: () => Promise.resolve({ data: { session: null }, error: null }),
      signUp: () => Promise.resolve({ data: { user: null }, error: { message: 'Supabase not configured' } }),
      signInWithPassword: () => Promise.resolve({ data: { user: null }, error: { message: 'Supabase not configured' } }),
      signOut: () => Promise.resolve({ error: null }),
      onAuthStateChange: () => ({ data: { subscription: { unsubscribe: () => {} } } })
    },
    from: () => ({
      select: () => Promise.resolve({ data: [], error: null }),
      insert: () => Promise.resolve({ data: null, error: { message: 'Supabase not configured' } }),
      update: () => Promise.resolve({ data: null, error: { message: 'Supabase not configured' } }),
      delete: () => Promise.resolve({ data: null, error: { message: 'Supabase not configured' } })
    })
  };
} else {
  try {
    supabase = createClient(supabaseUrl, supabaseAnonKey);
  } catch (error) {
    console.error('Failed to create Supabase client:', error);
    // Fallback to mock client
    supabase = {
      auth: {
        getUser: () => Promise.resolve({ data: { user: null }, error: null }),
        getSession: () => Promise.resolve({ data: { session: null }, error: null }),
        signUp: () => Promise.resolve({ data: { user: null }, error: { message: 'Supabase connection failed' } }),
        signInWithPassword: () => Promise.resolve({ data: { user: null }, error: { message: 'Supabase connection failed' } }),
        signOut: () => Promise.resolve({ error: null }),
        onAuthStateChange: () => ({ data: { subscription: { unsubscribe: () => {} } } })
      },
      from: () => ({
        select: () => Promise.resolve({ data: [], error: null }),
        insert: () => Promise.resolve({ data: null, error: { message: 'Supabase connection failed' } }),
        update: () => Promise.resolve({ data: null, error: { message: 'Supabase connection failed' } }),
        delete: () => Promise.resolve({ data: null, error: { message: 'Supabase connection failed' } })
      })
    };
  }
}

export { supabase };

// Database types
export interface DatabaseExercise {
  id: string;
  name: string;
  muscle_group: string;
  equipment: string;
  description: string;
  created_at: string;
  created_by: string | null;
  is_default: boolean;
}

export interface DatabaseWorkout {
  id: string;
  user_id: string;
  date: string;
  start_time: string;
  end_time: string | null;
  muscle_group: string;
  total_sets: number;
  total_reps: number;
  created_at: string;
}

export interface DatabaseExerciseSession {
  id: string;
  workout_id: string;
  exercise_id: string;
  exercise_name: string;
  start_time: string;
  end_time: string | null;
  muscle_group: string;
  max_weight: number;
  created_at: string;
}

export interface DatabaseWorkoutSet {
  id: string;
  exercise_session_id: string;
  reps: number;
  weight: number;
  set_order: number;
  created_at: string;
}