<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1755363122761" clover="3.2.0">
  <project timestamp="1755363122761" name="All files">
    <metrics statements="1158" coveredstatements="859" conditionals="535" coveredconditionals="341" methods="378" coveredmethods="258" elements="2071" coveredelements="1458" complexity="0" loc="1158" ncloc="1158" packages="5" files="19" classes="19"/>
    <package name="components">
      <metrics statements="658" coveredstatements="540" conditionals="335" coveredconditionals="232" methods="247" coveredmethods="176"/>
      <file name="AuthModal.tsx" path="/Users/<USER>/Downloads/my-workout/src/components/AuthModal.tsx">
        <metrics statements="32" coveredstatements="29" conditionals="36" coveredconditionals="29" methods="6" coveredmethods="6"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="10" count="1" type="stmt"/>
        <line num="11" count="32" type="stmt"/>
        <line num="12" count="32" type="stmt"/>
        <line num="13" count="32" type="stmt"/>
        <line num="14" count="32" type="stmt"/>
        <line num="15" count="32" type="stmt"/>
        <line num="17" count="32" type="stmt"/>
        <line num="18" count="6" type="stmt"/>
        <line num="19" count="6" type="stmt"/>
        <line num="20" count="6" type="stmt"/>
        <line num="22" count="6" type="stmt"/>
        <line num="23" count="6" type="cond" truecount="2" falsecount="0"/>
        <line num="24" count="1" type="stmt"/>
        <line num="26" count="5" type="stmt"/>
        <line num="28" count="2" type="stmt"/>
        <line num="29" count="2" type="stmt"/>
        <line num="32" count="1" type="cond" truecount="4" falsecount="2"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="1" type="cond" truecount="4" falsecount="2"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="1" type="cond" truecount="4" falsecount="2"/>
        <line num="37" count="0" type="stmt"/>
        <line num="39" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="42" count="3" type="stmt"/>
        <line num="46" count="32" type="stmt"/>
        <line num="71" count="6" type="stmt"/>
        <line num="88" count="6" type="stmt"/>
        <line num="121" count="2" type="stmt"/>
        <line num="157" count="1" type="stmt"/>
      </file>
      <file name="Dashboard.tsx" path="/Users/<USER>/Downloads/my-workout/src/components/Dashboard.tsx">
        <metrics statements="40" coveredstatements="37" conditionals="19" coveredconditionals="16" methods="14" coveredmethods="11"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="14" count="1" type="stmt"/>
        <line num="15" count="36" type="stmt"/>
        <line num="16" count="36" type="stmt"/>
        <line num="17" count="36" type="stmt"/>
        <line num="18" count="36" type="stmt"/>
        <line num="20" count="36" type="stmt"/>
        <line num="21" count="20" type="stmt"/>
        <line num="22" count="20" type="cond" truecount="1" falsecount="0"/>
        <line num="23" count="1" type="stmt"/>
        <line num="24" count="1" type="stmt"/>
        <line num="27" count="19" type="stmt"/>
        <line num="28" count="19" type="stmt"/>
        <line num="29" count="17" type="stmt"/>
        <line num="31" count="17" type="stmt"/>
        <line num="32" count="17" type="stmt"/>
        <line num="33" count="16" type="stmt"/>
        <line num="35" count="2" type="stmt"/>
        <line num="37" count="18" type="stmt"/>
        <line num="41" count="20" type="stmt"/>
        <line num="44" count="36" type="stmt"/>
        <line num="45" count="52" type="stmt"/>
        <line num="48" count="36" type="stmt"/>
        <line num="49" count="26" type="stmt"/>
        <line num="52" count="36" type="stmt"/>
        <line num="53" count="26" type="stmt"/>
        <line num="54" count="26" type="stmt"/>
        <line num="55" count="26" type="stmt"/>
        <line num="56" count="26" type="stmt"/>
        <line num="59" count="36" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="171" count="13" type="stmt"/>
        <line num="174" count="26" type="stmt"/>
        <line num="198" count="26" type="stmt"/>
        <line num="225" count="26" type="stmt"/>
        <line num="251" count="1" type="stmt"/>
      </file>
      <file name="ExerciseManager.tsx" path="/Users/<USER>/Downloads/my-workout/src/components/ExerciseManager.tsx">
        <metrics statements="71" coveredstatements="53" conditionals="20" coveredconditionals="12" methods="27" coveredmethods="21"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="12" count="1" type="stmt"/>
        <line num="13" count="23" type="stmt"/>
        <line num="14" count="23" type="stmt"/>
        <line num="15" count="23" type="stmt"/>
        <line num="16" count="23" type="stmt"/>
        <line num="17" count="23" type="stmt"/>
        <line num="24" count="23" type="stmt"/>
        <line num="25" count="9" type="stmt"/>
        <line num="28" count="23" type="stmt"/>
        <line num="29" count="10" type="stmt"/>
        <line num="30" count="10" type="stmt"/>
        <line num="31" count="9" type="stmt"/>
        <line num="33" count="1" type="stmt"/>
        <line num="35" count="10" type="stmt"/>
        <line num="39" count="23" type="stmt"/>
        <line num="40" count="1" type="stmt"/>
        <line num="41" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="42" count="0" type="stmt"/>
        <line num="44" count="1" type="stmt"/>
        <line num="47" count="1" type="stmt"/>
        <line num="48" count="1" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="54" count="23" type="stmt"/>
        <line num="55" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="65" count="23" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="76" count="23" type="stmt"/>
        <line num="77" count="1" type="stmt"/>
        <line num="78" count="1" type="stmt"/>
        <line num="79" count="1" type="stmt"/>
        <line num="87" count="23" type="stmt"/>
        <line num="88" count="26" type="cond" truecount="2" falsecount="2"/>
        <line num="90" count="0" type="stmt"/>
        <line num="92" count="13" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="96" count="13" type="stmt"/>
        <line num="100" count="23" type="stmt"/>
        <line num="101" count="26" type="cond" truecount="2" falsecount="2"/>
        <line num="103" count="0" type="stmt"/>
        <line num="105" count="13" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="109" count="13" type="stmt"/>
        <line num="116" count="23" type="stmt"/>
        <line num="117" count="23" type="stmt"/>
        <line num="119" count="23" type="cond" truecount="1" falsecount="0"/>
        <line num="120" count="9" type="stmt"/>
        <line num="130" count="14" type="stmt"/>
        <line num="137" count="2" type="stmt"/>
        <line num="166" count="1" type="stmt"/>
        <line num="178" count="1" type="stmt"/>
        <line num="182" count="36" type="stmt"/>
        <line num="195" count="1" type="stmt"/>
        <line num="199" count="18" type="stmt"/>
        <line num="213" count="1" type="stmt"/>
        <line num="242" count="26" type="stmt"/>
        <line num="251" count="0" type="stmt"/>
        <line num="252" count="0" type="stmt"/>
        <line num="265" count="0" type="stmt"/>
        <line num="271" count="0" type="stmt"/>
        <line num="296" count="1" type="stmt"/>
      </file>
      <file name="ExerciseSelector.tsx" path="/Users/<USER>/Downloads/my-workout/src/components/ExerciseSelector.tsx">
        <metrics statements="4" coveredstatements="4" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="1"/>
        <line num="2" count="1" type="stmt"/>
        <line num="14" count="1" type="stmt"/>
        <line num="20" count="15" type="stmt"/>
        <line num="29" count="1" type="stmt"/>
      </file>
      <file name="ExercisesByEquipment.tsx" path="/Users/<USER>/Downloads/my-workout/src/components/ExercisesByEquipment.tsx">
        <metrics statements="88" coveredstatements="77" conditionals="50" coveredconditionals="44" methods="34" coveredmethods="26"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="9" count="1" type="stmt"/>
        <line num="24" count="1" type="stmt"/>
        <line num="29" count="49" type="stmt"/>
        <line num="30" count="49" type="stmt"/>
        <line num="31" count="49" type="stmt"/>
        <line num="32" count="49" type="stmt"/>
        <line num="33" count="49" type="stmt"/>
        <line num="34" count="49" type="stmt"/>
        <line num="35" count="49" type="stmt"/>
        <line num="37" count="49" type="stmt"/>
        <line num="38" count="16" type="stmt"/>
        <line num="41" count="49" type="stmt"/>
        <line num="42" count="16" type="stmt"/>
        <line num="43" count="16" type="stmt"/>
        <line num="46" count="16" type="stmt"/>
        <line num="49" count="16" type="cond" truecount="2" falsecount="0"/>
        <line num="51" count="1" type="stmt"/>
        <line num="52" count="1" type="stmt"/>
        <line num="55" count="15" type="stmt"/>
        <line num="56" count="14" type="stmt"/>
        <line num="59" count="15" type="stmt"/>
        <line num="62" count="15" type="stmt"/>
        <line num="63" count="15" type="stmt"/>
        <line num="66" count="15" type="stmt"/>
        <line num="67" count="29" type="stmt"/>
        <line num="68" count="29" type="stmt"/>
        <line num="70" count="15" type="stmt"/>
        <line num="72" count="15" type="stmt"/>
        <line num="74" count="1" type="stmt"/>
        <line num="76" count="16" type="stmt"/>
        <line num="80" count="49" type="stmt"/>
        <line num="81" count="4" type="stmt"/>
        <line num="82" count="4" type="cond" truecount="2" falsecount="0"/>
        <line num="83" count="1" type="stmt"/>
        <line num="88" count="49" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="92" count="49" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="96" count="49" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="100" count="49" type="stmt"/>
        <line num="101" count="8" type="stmt"/>
        <line num="107" count="49" type="stmt"/>
        <line num="108" count="60" type="cond" truecount="3" falsecount="3"/>
        <line num="110" count="29" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="116" count="30" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="120" count="1" type="stmt"/>
        <line num="124" count="49" type="stmt"/>
        <line num="125" count="60" type="stmt"/>
        <line num="126" count="61" type="stmt"/>
        <line num="130" count="49" type="stmt"/>
        <line num="131" count="32" type="stmt"/>
        <line num="132" count="33" type="stmt"/>
        <line num="137" count="49" type="stmt"/>
        <line num="138" count="63" type="stmt"/>
        <line num="139" count="94" type="stmt"/>
        <line num="142" count="63" type="cond" truecount="1" falsecount="0"/>
        <line num="143" count="60" type="stmt"/>
        <line num="146" count="63" type="stmt"/>
        <line num="149" count="49" type="cond" truecount="1" falsecount="0"/>
        <line num="150" count="16" type="stmt"/>
        <line num="161" count="33" type="stmt"/>
        <line num="186" count="2" type="stmt"/>
        <line num="194" count="60" type="stmt"/>
        <line num="197" count="8" type="stmt"/>
        <line num="225" count="89" type="stmt"/>
        <line num="227" count="3" type="stmt"/>
        <line num="248" count="0" type="stmt"/>
        <line num="249" count="0" type="stmt"/>
        <line num="273" count="3" type="stmt"/>
        <line num="274" count="3" type="stmt"/>
        <line num="318" count="1" type="stmt"/>
        <line num="319" count="1" type="stmt"/>
        <line num="337" count="0" type="stmt"/>
        <line num="354" count="0" type="stmt"/>
        <line num="355" count="0" type="stmt"/>
        <line num="417" count="1" type="stmt"/>
        <line num="418" count="1" type="stmt"/>
        <line num="429" count="1" type="stmt"/>
        <line num="444" count="1" type="stmt"/>
      </file>
      <file name="MuscleGroupSelector.tsx" path="/Users/<USER>/Downloads/my-workout/src/components/MuscleGroupSelector.tsx">
        <metrics statements="55" coveredstatements="53" conditionals="28" coveredconditionals="25" methods="23" coveredmethods="23"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="12" count="45" type="stmt"/>
        <line num="13" count="45" type="stmt"/>
        <line num="14" count="45" type="stmt"/>
        <line num="15" count="45" type="stmt"/>
        <line num="17" count="45" type="stmt"/>
        <line num="18" count="17" type="stmt"/>
        <line num="21" count="45" type="stmt"/>
        <line num="22" count="17" type="stmt"/>
        <line num="23" count="17" type="stmt"/>
        <line num="24" count="17" type="stmt"/>
        <line num="25" count="16" type="stmt"/>
        <line num="27" count="1" type="stmt"/>
        <line num="29" count="17" type="stmt"/>
        <line num="33" count="45" type="stmt"/>
        <line num="34" count="6" type="stmt"/>
        <line num="40" count="45" type="stmt"/>
        <line num="41" count="10" type="cond" truecount="6" falsecount="1"/>
        <line num="43" count="4" type="stmt"/>
        <line num="45" count="4" type="stmt"/>
        <line num="48" count="1" type="stmt"/>
        <line num="51" count="1" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="57" count="45" type="stmt"/>
        <line num="58" count="10" type="cond" truecount="6" falsecount="1"/>
        <line num="60" count="4" type="stmt"/>
        <line num="62" count="4" type="stmt"/>
        <line num="65" count="1" type="stmt"/>
        <line num="68" count="1" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="74" count="45" type="stmt"/>
        <line num="75" count="69" type="stmt"/>
        <line num="78" count="45" type="stmt"/>
        <line num="79" count="10" type="stmt"/>
        <line num="83" count="45" type="stmt"/>
        <line num="84" count="79" type="cond" truecount="3" falsecount="0"/>
        <line num="86" count="24" type="stmt"/>
        <line num="89" count="26" type="stmt"/>
        <line num="93" count="45" type="cond" truecount="1" falsecount="0"/>
        <line num="94" count="17" type="stmt"/>
        <line num="105" count="28" type="stmt"/>
        <line num="127" count="6" type="stmt"/>
        <line num="145" count="79" type="stmt"/>
        <line num="153" count="79" type="stmt"/>
        <line num="154" count="157" type="stmt"/>
        <line num="172" count="69" type="stmt"/>
        <line num="174" count="6" type="stmt"/>
        <line num="183" count="137" type="stmt"/>
        <line num="198" count="10" type="stmt"/>
        <line num="200" count="1" type="stmt"/>
        <line num="213" count="14" type="stmt"/>
        <line num="239" count="1" type="stmt"/>
      </file>
      <file name="PrimaryMuscleSelector.tsx" path="/Users/<USER>/Downloads/my-workout/src/components/PrimaryMuscleSelector.tsx">
        <metrics statements="52" coveredstatements="52" conditionals="20" coveredconditionals="17" methods="22" coveredmethods="22"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="44" count="1" type="stmt"/>
        <line num="45" count="34" type="stmt"/>
        <line num="46" count="34" type="stmt"/>
        <line num="47" count="34" type="stmt"/>
        <line num="48" count="34" type="stmt"/>
        <line num="50" count="34" type="stmt"/>
        <line num="51" count="15" type="stmt"/>
        <line num="54" count="34" type="stmt"/>
        <line num="55" count="15" type="stmt"/>
        <line num="56" count="15" type="stmt"/>
        <line num="59" count="15" type="stmt"/>
        <line num="62" count="14" type="stmt"/>
        <line num="64" count="80" type="stmt"/>
        <line num="69" count="14" type="stmt"/>
        <line num="70" count="14" type="stmt"/>
        <line num="71" count="105" type="stmt"/>
        <line num="73" count="14" type="stmt"/>
        <line num="76" count="14" type="stmt"/>
        <line num="77" count="84" type="stmt"/>
        <line num="78" count="224" type="cond" truecount="2" falsecount="0"/>
        <line num="81" count="84" type="stmt"/>
        <line num="83" count="224" type="stmt"/>
        <line num="86" count="84" type="stmt"/>
        <line num="88" count="14" type="stmt"/>
        <line num="90" count="14" type="stmt"/>
        <line num="92" count="1" type="stmt"/>
        <line num="94" count="15" type="stmt"/>
        <line num="98" count="34" type="stmt"/>
        <line num="99" count="69" type="stmt"/>
        <line num="108" count="69" type="cond" truecount="1" falsecount="1"/>
        <line num="111" count="34" type="stmt"/>
        <line num="112" count="138" type="cond" truecount="4" falsecount="2"/>
        <line num="116" count="34" type="stmt"/>
        <line num="117" count="86" type="cond" truecount="2" falsecount="0"/>
        <line num="118" count="20" type="stmt"/>
        <line num="122" count="34" type="stmt"/>
        <line num="123" count="1" type="stmt"/>
        <line num="124" count="1" type="cond" truecount="3" falsecount="0"/>
        <line num="126" count="1" type="stmt"/>
        <line num="130" count="34" type="cond" truecount="1" falsecount="0"/>
        <line num="131" count="15" type="stmt"/>
        <line num="142" count="19" type="stmt"/>
        <line num="164" count="5" type="stmt"/>
        <line num="179" count="103" type="stmt"/>
        <line num="193" count="69" type="stmt"/>
        <line num="195" count="1" type="stmt"/>
        <line num="210" count="84" type="stmt"/>
        <line num="236" count="1" type="stmt"/>
      </file>
      <file name="SelectedExercisesList.tsx" path="/Users/<USER>/Downloads/my-workout/src/components/SelectedExercisesList.tsx">
        <metrics statements="31" coveredstatements="31" conditionals="10" coveredconditionals="8" methods="10" coveredmethods="10"/>
        <line num="2" count="1" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="17" count="1" type="stmt"/>
        <line num="20" count="1" type="stmt"/>
        <line num="21" count="1" type="stmt"/>
        <line num="23" count="1" type="stmt"/>
        <line num="40" count="1" type="stmt"/>
        <line num="53" count="43" type="stmt"/>
        <line num="55" count="43" type="stmt"/>
        <line num="61" count="43" type="stmt"/>
        <line num="90" count="2" type="stmt"/>
        <line num="91" count="2" type="stmt"/>
        <line num="118" count="1" type="stmt"/>
        <line num="125" count="1" type="stmt"/>
        <line num="136" count="1" type="stmt"/>
        <line num="143" count="25" type="stmt"/>
        <line num="151" count="25" type="stmt"/>
        <line num="152" count="68" type="stmt"/>
        <line num="153" count="46" type="stmt"/>
        <line num="155" count="25" type="stmt"/>
        <line num="156" count="3" type="stmt"/>
        <line num="158" count="3" type="cond" truecount="3" falsecount="0"/>
        <line num="159" count="1" type="stmt"/>
        <line num="160" count="1" type="stmt"/>
        <line num="162" count="1" type="stmt"/>
        <line num="163" count="1" type="stmt"/>
        <line num="167" count="25" type="cond" truecount="1" falsecount="0"/>
        <line num="168" count="2" type="stmt"/>
        <line num="171" count="23" type="stmt"/>
        <line num="193" count="43" type="stmt"/>
        <line num="208" count="1" type="stmt"/>
      </file>
      <file name="WorkoutFlow.tsx" path="/Users/<USER>/Downloads/my-workout/src/components/WorkoutFlow.tsx">
        <metrics statements="36" coveredstatements="35" conditionals="8" coveredconditionals="8" methods="10" coveredmethods="10"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="12" count="56" type="stmt"/>
        <line num="13" count="56" type="stmt"/>
        <line num="14" count="56" type="stmt"/>
        <line num="15" count="56" type="stmt"/>
        <line num="16" count="56" type="stmt"/>
        <line num="18" count="56" type="stmt"/>
        <line num="19" count="14" type="stmt"/>
        <line num="22" count="56" type="stmt"/>
        <line num="23" count="8" type="stmt"/>
        <line num="24" count="8" type="stmt"/>
        <line num="27" count="56" type="stmt"/>
        <line num="28" count="5" type="stmt"/>
        <line num="29" count="5" type="stmt"/>
        <line num="32" count="56" type="stmt"/>
        <line num="33" count="1" type="stmt"/>
        <line num="36" count="56" type="cond" truecount="1" falsecount="0"/>
        <line num="37" count="27" type="stmt"/>
        <line num="65" count="2" type="stmt"/>
        <line num="77" count="2" type="stmt"/>
        <line num="96" count="29" type="cond" truecount="1" falsecount="0"/>
        <line num="97" count="15" type="stmt"/>
        <line num="99" count="1" type="stmt"/>
        <line num="105" count="14" type="cond" truecount="3" falsecount="0"/>
        <line num="106" count="9" type="stmt"/>
        <line num="109" count="1" type="stmt"/>
        <line num="115" count="5" type="cond" truecount="3" falsecount="0"/>
        <line num="116" count="5" type="stmt"/>
        <line num="123" count="1" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="131" count="1" type="stmt"/>
      </file>
      <file name="WorkoutHistory.tsx" path="/Users/<USER>/Downloads/my-workout/src/components/WorkoutHistory.tsx">
        <metrics statements="118" coveredstatements="84" conditionals="51" coveredconditionals="17" methods="50" coveredmethods="29"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="12" count="1" type="stmt"/>
        <line num="13" count="74" type="stmt"/>
        <line num="14" count="74" type="stmt"/>
        <line num="15" count="74" type="stmt"/>
        <line num="16" count="74" type="stmt"/>
        <line num="17" count="74" type="stmt"/>
        <line num="18" count="74" type="stmt"/>
        <line num="20" count="74" type="stmt"/>
        <line num="21" count="28" type="stmt"/>
        <line num="22" count="28" type="stmt"/>
        <line num="25" count="74" type="stmt"/>
        <line num="26" count="30" type="stmt"/>
        <line num="27" count="30" type="stmt"/>
        <line num="28" count="28" type="stmt"/>
        <line num="30" count="1" type="stmt"/>
        <line num="32" count="29" type="stmt"/>
        <line num="36" count="74" type="stmt"/>
        <line num="37" count="28" type="stmt"/>
        <line num="38" count="28" type="stmt"/>
        <line num="39" count="27" type="stmt"/>
        <line num="41" count="1" type="stmt"/>
        <line num="45" count="74" type="stmt"/>
        <line num="46" count="11" type="stmt"/>
        <line num="47" count="11" type="stmt"/>
        <line num="50" count="74" type="stmt"/>
        <line num="51" count="1" type="stmt"/>
        <line num="52" count="1" type="stmt"/>
        <line num="53" count="1" type="stmt"/>
        <line num="56" count="74" type="stmt"/>
        <line num="57" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="59" count="1" type="stmt"/>
        <line num="61" count="2" type="stmt"/>
        <line num="62" count="1" type="stmt"/>
        <line num="63" count="6" type="stmt"/>
        <line num="66" count="1" type="stmt"/>
        <line num="73" count="1" type="stmt"/>
        <line num="75" count="1" type="stmt"/>
        <line num="76" count="1" type="stmt"/>
        <line num="77" count="1" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="83" count="74" type="stmt"/>
        <line num="84" count="2" type="cond" truecount="1" falsecount="0"/>
        <line num="85" count="1" type="stmt"/>
        <line num="86" count="1" type="stmt"/>
        <line num="87" count="1" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="94" count="74" type="stmt"/>
        <line num="95" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="107" count="74" type="stmt"/>
        <line num="108" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="115" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="117" count="0" type="stmt"/>
        <line num="120" count="74" type="stmt"/>
        <line num="121" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="127" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="128" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="134" count="74" type="stmt"/>
        <line num="135" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="137" count="1" type="stmt"/>
        <line num="138" count="1" type="stmt"/>
        <line num="139" count="1" type="stmt"/>
        <line num="140" count="1" type="stmt"/>
        <line num="143" count="74" type="stmt"/>
        <line num="144" count="104" type="stmt"/>
        <line num="147" count="74" type="stmt"/>
        <line num="148" count="52" type="stmt"/>
        <line num="156" count="74" type="stmt"/>
        <line num="157" count="69" type="stmt"/>
        <line num="158" count="69" type="stmt"/>
        <line num="159" count="69" type="stmt"/>
        <line num="160" count="69" type="stmt"/>
        <line num="166" count="74" type="cond" truecount="1" falsecount="0"/>
        <line num="167" count="28" type="stmt"/>
        <line num="177" count="46" type="cond" truecount="1" falsecount="0"/>
        <line num="178" count="17" type="stmt"/>
        <line num="214" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="223" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="232" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="246" count="32" type="stmt"/>
        <line num="251" count="5" type="cond" truecount="1" falsecount="1"/>
        <line num="257" count="1" type="stmt"/>
        <line num="272" count="0" type="stmt"/>
        <line num="273" count="0" type="stmt"/>
        <line num="274" count="0" type="stmt"/>
        <line num="285" count="0" type="stmt"/>
        <line num="286" count="0" type="stmt"/>
        <line num="287" count="0" type="stmt"/>
        <line num="297" count="96" type="stmt"/>
        <line num="306" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="316" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="322" count="0" type="stmt"/>
        <line num="344" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="355" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="384" count="29" type="stmt"/>
        <line num="412" count="52" type="stmt"/>
        <line num="436" count="11" type="stmt"/>
        <line num="443" count="2" type="stmt"/>
        <line num="454" count="71" type="stmt"/>
        <line num="462" count="1" type="stmt"/>
        <line num="463" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="476" count="187" type="stmt"/>
        <line num="498" count="1" type="stmt"/>
      </file>
      <file name="WorkoutSession.tsx" path="/Users/<USER>/Downloads/my-workout/src/components/WorkoutSession.tsx">
        <metrics statements="131" coveredstatements="85" conditionals="93" coveredconditionals="56" methods="50" coveredmethods="17"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="18" count="1" type="stmt"/>
        <line num="26" count="82" type="stmt"/>
        <line num="27" count="82" type="stmt"/>
        <line num="28" count="82" type="stmt"/>
        <line num="29" count="82" type="stmt"/>
        <line num="30" count="82" type="stmt"/>
        <line num="31" count="82" type="stmt"/>
        <line num="32" count="82" type="stmt"/>
        <line num="33" count="82" type="stmt"/>
        <line num="34" count="82" type="stmt"/>
        <line num="35" count="82" type="stmt"/>
        <line num="36" count="82" type="stmt"/>
        <line num="37" count="82" type="stmt"/>
        <line num="39" count="82" type="stmt"/>
        <line num="41" count="82" type="stmt"/>
        <line num="42" count="36" type="stmt"/>
        <line num="43" count="36" type="stmt"/>
        <line num="44" count="36" type="stmt"/>
        <line num="45" count="36" type="stmt"/>
        <line num="46" count="34" type="stmt"/>
        <line num="47" count="64" type="stmt"/>
        <line num="48" count="64" type="stmt"/>
        <line num="51" count="64" type="stmt"/>
        <line num="52" count="64" type="stmt"/>
        <line num="54" count="64" type="stmt"/>
        <line num="57" count="62" type="cond" truecount="0" falsecount="1"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="60" count="0" type="stmt"/>
        <line num="64" count="62" type="cond" truecount="1" falsecount="0"/>
        <line num="65" count="62" type="stmt"/>
        <line num="66" count="62" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="80" count="2" type="stmt"/>
        <line num="81" count="2" type="stmt"/>
        <line num="85" count="34" type="stmt"/>
        <line num="87" count="64" type="stmt"/>
        <line num="89" count="34" type="cond" truecount="1" falsecount="0"/>
        <line num="90" count="2" type="stmt"/>
        <line num="91" count="2" type="stmt"/>
        <line num="92" count="2" type="stmt"/>
        <line num="96" count="64" type="stmt"/>
        <line num="107" count="32" type="stmt"/>
        <line num="108" count="32" type="stmt"/>
        <line num="110" count="1" type="stmt"/>
        <line num="112" count="35" type="stmt"/>
        <line num="116" count="36" type="stmt"/>
        <line num="119" count="82" type="stmt"/>
        <line num="121" count="36" type="stmt"/>
        <line num="122" count="36" type="stmt"/>
        <line num="123" count="36" type="stmt"/>
        <line num="125" count="36" type="stmt"/>
        <line num="127" count="36" type="stmt"/>
        <line num="128" count="131" type="stmt"/>
        <line num="129" count="131" type="stmt"/>
        <line num="130" count="131" type="stmt"/>
        <line num="131" count="131" type="stmt"/>
        <line num="134" count="36" type="stmt"/>
        <line num="135" count="36" type="stmt"/>
        <line num="137" count="36" type="stmt"/>
        <line num="140" count="82" type="stmt"/>
        <line num="141" count="36" type="stmt"/>
        <line num="142" count="36" type="stmt"/>
        <line num="143" count="36" type="stmt"/>
        <line num="146" count="82" type="stmt"/>
        <line num="148" count="7" type="stmt"/>
        <line num="149" count="7" type="stmt"/>
        <line num="150" count="7" type="stmt"/>
        <line num="151" count="7" type="stmt"/>
        <line num="154" count="82" type="stmt"/>
        <line num="155" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="156" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="161" count="82" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="165" count="82" type="stmt"/>
        <line num="166" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="167" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="184" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="185" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="191" count="82" type="stmt"/>
        <line num="192" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="193" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="202" count="82" type="stmt"/>
        <line num="203" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="204" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
        <line num="210" count="82" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="212" count="0" type="stmt"/>
        <line num="213" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="227" count="0" type="stmt"/>
        <line num="228" count="0" type="stmt"/>
        <line num="230" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="235" count="82" type="cond" truecount="1" falsecount="0"/>
        <line num="236" count="72" type="stmt"/>
        <line num="246" count="10" type="cond" truecount="1" falsecount="0"/>
        <line num="247" count="3" type="stmt"/>
        <line num="262" count="7" type="stmt"/>
        <line num="303" count="0" type="stmt"/>
        <line num="304" count="0" type="stmt"/>
        <line num="323" count="0" type="stmt"/>
        <line num="349" count="28" type="stmt"/>
        <line num="360" count="0" type="stmt"/>
        <line num="439" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="453" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="474" count="0" type="stmt"/>
        <line num="479" count="0" type="stmt"/>
        <line num="500" count="0" type="stmt"/>
        <line num="509" count="0" type="stmt"/>
        <line num="524" count="14" type="stmt"/>
        <line num="549" count="0" type="stmt"/>
        <line num="585" count="0" type="stmt"/>
        <line num="605" count="1" type="stmt"/>
      </file>
    </package>
    <package name="data">
      <metrics statements="2" coveredstatements="2" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="exercises.ts" path="/Users/<USER>/Downloads/my-workout/src/data/exercises.ts">
        <metrics statements="2" coveredstatements="2" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="9" count="4" type="stmt"/>
        <line num="12" count="4" type="stmt"/>
      </file>
    </package>
    <package name="lib">
      <metrics statements="32" coveredstatements="6" conditionals="4" coveredconditionals="2" methods="24" coveredmethods="0"/>
      <file name="supabase.ts" path="/Users/<USER>/Downloads/my-workout/src/lib/supabase.ts">
        <metrics statements="32" coveredstatements="6" conditionals="4" coveredconditionals="2" methods="24" coveredmethods="0"/>
        <line num="1" count="3" type="stmt"/>
        <line num="3" count="3" type="stmt"/>
        <line num="4" count="3" type="stmt"/>
        <line num="9" count="3" type="cond" truecount="2" falsecount="2"/>
        <line num="10" count="3" type="stmt"/>
        <line num="13" count="3" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
      </file>
    </package>
    <package name="services">
      <metrics statements="386" coveredstatements="231" conditionals="165" coveredconditionals="77" methods="90" coveredmethods="65"/>
      <file name="authService.ts" path="/Users/<USER>/Downloads/my-workout/src/services/authService.ts">
        <metrics statements="20" coveredstatements="20" conditionals="9" coveredconditionals="9" methods="6" coveredmethods="6"/>
        <line num="1" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="7" count="2" type="stmt"/>
        <line num="12" count="2" type="cond" truecount="1" falsecount="0"/>
        <line num="13" count="1" type="stmt"/>
        <line num="14" count="1" type="stmt"/>
        <line num="17" count="1" type="stmt"/>
        <line num="22" count="2" type="stmt"/>
        <line num="27" count="2" type="cond" truecount="1" falsecount="0"/>
        <line num="28" count="1" type="stmt"/>
        <line num="29" count="1" type="stmt"/>
        <line num="32" count="1" type="stmt"/>
        <line num="37" count="2" type="stmt"/>
        <line num="39" count="2" type="cond" truecount="1" falsecount="0"/>
        <line num="40" count="1" type="stmt"/>
        <line num="41" count="1" type="stmt"/>
        <line num="47" count="2" type="stmt"/>
        <line num="48" count="2" type="stmt"/>
        <line num="53" count="3" type="stmt"/>
        <line num="54" count="2" type="cond" truecount="6" falsecount="0"/>
      </file>
      <file name="exerciseDataService.ts" path="/Users/<USER>/Downloads/my-workout/src/services/exerciseDataService.ts">
        <metrics statements="104" coveredstatements="92" conditionals="43" coveredconditionals="29" methods="25" coveredmethods="25"/>
        <line num="26" count="3" type="stmt"/>
        <line num="46" count="3" type="stmt"/>
        <line num="67" count="3" type="stmt"/>
        <line num="68" count="3" type="stmt"/>
        <line num="69" count="3" type="stmt"/>
        <line num="75" count="5" type="cond" truecount="1" falsecount="0"/>
        <line num="76" count="1" type="stmt"/>
        <line num="79" count="4" type="stmt"/>
        <line num="81" count="4" type="stmt"/>
        <line num="82" count="4" type="stmt"/>
        <line num="85" count="4" type="stmt"/>
        <line num="86" count="4" type="stmt"/>
        <line num="87" count="3" type="stmt"/>
        <line num="88" count="5" type="stmt"/>
        <line num="89" count="5" type="stmt"/>
        <line num="90" count="5" type="stmt"/>
        <line num="91" count="5" type="cond" truecount="1" falsecount="1"/>
        <line num="92" count="5" type="stmt"/>
        <line num="93" count="5" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="103" count="3" type="stmt"/>
        <line num="104" count="5" type="stmt"/>
        <line num="105" count="3" type="stmt"/>
        <line num="108" count="3" type="stmt"/>
        <line num="111" count="4" type="stmt"/>
        <line num="112" count="4" type="stmt"/>
        <line num="113" count="4" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="124" count="4" type="stmt"/>
        <line num="125" count="4" type="stmt"/>
        <line num="126" count="3" type="cond" truecount="1" falsecount="1"/>
        <line num="127" count="3" type="stmt"/>
        <line num="128" count="3" type="stmt"/>
        <line num="129" count="3" type="cond" truecount="1" falsecount="1"/>
        <line num="131" count="0" type="stmt"/>
        <line num="134" count="1" type="stmt"/>
        <line num="138" count="1" type="stmt"/>
        <line num="139" count="1" type="stmt"/>
        <line num="146" count="5" type="stmt"/>
        <line num="147" count="5" type="cond" truecount="4" falsecount="0"/>
        <line num="148" count="1" type="stmt"/>
        <line num="151" count="4" type="stmt"/>
        <line num="152" count="4" type="cond" truecount="1" falsecount="1"/>
        <line num="155" count="4" type="cond" truecount="1" falsecount="1"/>
        <line num="160" count="4" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="185" count="18" type="cond" truecount="1" falsecount="0"/>
        <line num="186" count="2" type="stmt"/>
        <line num="189" count="16" type="stmt"/>
        <line num="190" count="16" type="stmt"/>
        <line num="192" count="16" type="stmt"/>
        <line num="194" count="27" type="stmt"/>
        <line num="196" count="27" type="cond" truecount="0" falsecount="1"/>
        <line num="197" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="201" count="27" type="cond" truecount="1" falsecount="1"/>
        <line num="204" count="27" type="cond" truecount="1" falsecount="0"/>
        <line num="205" count="22" type="stmt"/>
        <line num="209" count="27" type="cond" truecount="1" falsecount="0"/>
        <line num="210" count="25" type="stmt"/>
        <line num="213" count="27" type="stmt"/>
        <line num="217" count="16" type="stmt"/>
        <line num="218" count="22" type="stmt"/>
        <line num="219" count="25" type="stmt"/>
        <line num="223" count="16" type="stmt"/>
        <line num="224" count="16" type="stmt"/>
        <line num="225" count="16" type="stmt"/>
        <line num="232" count="8" type="stmt"/>
        <line num="233" count="8" type="cond" truecount="2" falsecount="0"/>
        <line num="240" count="2" type="stmt"/>
        <line num="241" count="2" type="stmt"/>
        <line num="243" count="2" type="stmt"/>
        <line num="244" count="3" type="cond" truecount="2" falsecount="0"/>
        <line num="245" count="3" type="stmt"/>
        <line num="246" count="2" type="cond" truecount="1" falsecount="0"/>
        <line num="247" count="1" type="stmt"/>
        <line num="249" count="2" type="stmt"/>
        <line num="254" count="2" type="stmt"/>
        <line num="255" count="1" type="stmt"/>
        <line num="256" count="3" type="stmt"/>
        <line num="258" count="1" type="stmt"/>
        <line num="261" count="2" type="stmt"/>
        <line num="268" count="2" type="stmt"/>
        <line num="269" count="2" type="stmt"/>
        <line num="276" count="2" type="stmt"/>
        <line num="278" count="2" type="stmt"/>
        <line num="279" count="2" type="stmt"/>
        <line num="280" count="1" type="cond" truecount="1" falsecount="2"/>
        <line num="281" count="1" type="cond" truecount="3" falsecount="0"/>
        <line num="282" count="0" type="stmt"/>
        <line num="290" count="20" type="stmt"/>
        <line num="291" count="20" type="stmt"/>
        <line num="298" count="4" type="stmt"/>
        <line num="299" count="4" type="stmt"/>
        <line num="301" count="4" type="cond" truecount="0" falsecount="1"/>
        <line num="302" count="0" type="stmt"/>
        <line num="305" count="4" type="stmt"/>
        <line num="306" count="4" type="stmt"/>
        <line num="307" count="4" type="stmt"/>
        <line num="313" count="3" type="stmt"/>
      </file>
      <file name="exerciseService.ts" path="/Users/<USER>/Downloads/my-workout/src/services/exerciseService.ts">
        <metrics statements="156" coveredstatements="44" conditionals="74" coveredconditionals="22" methods="31" coveredmethods="12"/>
        <line num="2" count="2" type="stmt"/>
        <line num="5" count="2" type="stmt"/>
        <line num="46" count="2" type="stmt"/>
        <line num="49" count="3" type="cond" truecount="0" falsecount="1"/>
        <line num="50" count="3" type="stmt"/>
        <line num="51" count="3" type="stmt"/>
        <line num="52" count="3" type="stmt"/>
        <line num="53" count="2" type="cond" truecount="1" falsecount="0"/>
        <line num="54" count="1" type="stmt"/>
        <line num="55" count="1" type="stmt"/>
        <line num="57" count="1" type="stmt"/>
        <line num="58" count="1" type="stmt"/>
        <line num="59" count="1" type="stmt"/>
        <line num="61" count="2" type="stmt"/>
        <line num="62" count="2" type="stmt"/>
        <line num="67" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="68" count="3" type="stmt"/>
        <line num="73" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="74" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="90" count="3" type="stmt"/>
        <line num="92" count="3" type="cond" truecount="6" falsecount="0"/>
        <line num="97" count="2" type="stmt"/>
        <line num="98" count="2" type="stmt"/>
        <line num="102" count="1" type="stmt"/>
        <line num="120" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="125" count="1" type="stmt"/>
        <line num="169" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="170" count="1" type="stmt"/>
        <line num="172" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="173" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="177" count="1" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="197" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="198" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="202" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="204" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="206" count="0" type="stmt"/>
        <line num="209" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="210" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="214" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="223" count="0" type="stmt"/>
        <line num="224" count="0" type="stmt"/>
        <line num="229" count="2" type="stmt"/>
        <line num="231" count="3" type="cond" truecount="3" falsecount="1"/>
        <line num="235" count="3" type="cond" truecount="1" falsecount="0"/>
        <line num="236" count="2" type="stmt"/>
        <line num="239" count="3" type="stmt"/>
        <line num="240" count="3" type="stmt"/>
        <line num="246" count="0" type="stmt"/>
        <line num="248" count="0" type="stmt"/>
        <line num="249" count="0" type="stmt"/>
        <line num="251" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="252" count="0" type="stmt"/>
        <line num="253" count="0" type="stmt"/>
        <line num="254" count="0" type="stmt"/>
        <line num="255" count="0" type="stmt"/>
        <line num="257" count="0" type="stmt"/>
        <line num="259" count="0" type="stmt"/>
        <line num="260" count="0" type="stmt"/>
        <line num="263" count="0" type="stmt"/>
        <line num="264" count="0" type="stmt"/>
        <line num="268" count="0" type="stmt"/>
        <line num="274" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="275" count="0" type="stmt"/>
        <line num="276" count="0" type="stmt"/>
        <line num="279" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="280" count="0" type="stmt"/>
        <line num="283" count="0" type="stmt"/>
        <line num="284" count="0" type="stmt"/>
        <line num="286" count="0" type="stmt"/>
        <line num="287" count="0" type="stmt"/>
        <line num="289" count="0" type="stmt"/>
        <line num="290" count="0" type="stmt"/>
        <line num="298" count="0" type="stmt"/>
        <line num="299" count="0" type="stmt"/>
        <line num="302" count="0" type="stmt"/>
        <line num="303" count="0" type="stmt"/>
        <line num="306" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="307" count="0" type="stmt"/>
        <line num="310" count="0" type="stmt"/>
        <line num="316" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="317" count="0" type="stmt"/>
        <line num="318" count="0" type="stmt"/>
        <line num="320" count="0" type="stmt"/>
        <line num="323" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="324" count="0" type="stmt"/>
        <line num="327" count="0" type="stmt"/>
        <line num="330" count="0" type="stmt"/>
        <line num="331" count="0" type="stmt"/>
        <line num="334" count="0" type="stmt"/>
        <line num="336" count="0" type="stmt"/>
        <line num="337" count="0" type="stmt"/>
        <line num="344" count="0" type="stmt"/>
        <line num="346" count="0" type="stmt"/>
        <line num="347" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="348" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="350" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="352" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="355" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="356" count="0" type="stmt"/>
        <line num="360" count="0" type="stmt"/>
        <line num="366" count="8" type="cond" truecount="1" falsecount="1"/>
        <line num="370" count="1" type="stmt"/>
        <line num="371" count="6" type="stmt"/>
        <line num="376" count="2" type="stmt"/>
        <line num="378" count="2" type="cond" truecount="1" falsecount="0"/>
        <line num="379" count="1" type="stmt"/>
        <line num="382" count="1" type="stmt"/>
        <line num="395" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="396" count="0" type="stmt"/>
        <line num="397" count="0" type="stmt"/>
        <line num="400" count="1" type="stmt"/>
        <line num="405" count="0" type="stmt"/>
        <line num="413" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="414" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="415" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="416" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="418" count="0" type="stmt"/>
        <line num="426" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="427" count="0" type="stmt"/>
        <line num="428" count="0" type="stmt"/>
        <line num="431" count="0" type="stmt"/>
        <line num="436" count="0" type="stmt"/>
        <line num="442" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="443" count="0" type="stmt"/>
        <line num="444" count="0" type="stmt"/>
        <line num="451" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="452" count="0" type="stmt"/>
        <line num="453" count="0" type="stmt"/>
        <line num="454" count="0" type="stmt"/>
        <line num="456" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="457" count="0" type="stmt"/>
        <line num="462" count="0" type="stmt"/>
        <line num="468" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="469" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="470" count="0" type="stmt"/>
        <line num="472" count="0" type="stmt"/>
        <line num="473" count="0" type="stmt"/>
        <line num="476" count="0" type="stmt"/>
        <line num="481" count="2" type="stmt"/>
      </file>
      <file name="workoutService.ts" path="/Users/<USER>/Downloads/my-workout/src/services/workoutService.ts">
        <metrics statements="106" coveredstatements="75" conditionals="39" coveredconditionals="17" methods="28" coveredmethods="22"/>
        <line num="1" count="3" type="stmt"/>
        <line num="5" count="3" type="stmt"/>
        <line num="8" count="3" type="stmt"/>
        <line num="10" count="3" type="cond" truecount="1" falsecount="0"/>
        <line num="11" count="1" type="stmt"/>
        <line num="15" count="2" type="stmt"/>
        <line num="29" count="2" type="cond" truecount="1" falsecount="0"/>
        <line num="30" count="1" type="stmt"/>
        <line num="31" count="1" type="stmt"/>
        <line num="35" count="1" type="stmt"/>
        <line num="36" count="1" type="stmt"/>
        <line num="37" count="1" type="stmt"/>
        <line num="52" count="1" type="stmt"/>
        <line num="53" count="1" type="stmt"/>
        <line num="64" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="69" count="1" type="stmt"/>
        <line num="72" count="1" type="stmt"/>
        <line num="74" count="1" type="stmt"/>
        <line num="80" count="1" type="stmt"/>
        <line num="82" count="1" type="stmt"/>
        <line num="96" count="2" type="stmt"/>
        <line num="98" count="2" type="cond" truecount="1" falsecount="0"/>
        <line num="99" count="1" type="stmt"/>
        <line num="102" count="1" type="stmt"/>
        <line num="115" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="116" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="120" count="1" type="stmt"/>
        <line num="125" count="2" type="stmt"/>
        <line num="127" count="2" type="cond" truecount="1" falsecount="0"/>
        <line num="128" count="1" type="stmt"/>
        <line num="131" count="1" type="stmt"/>
        <line num="132" count="1" type="stmt"/>
        <line num="134" count="1" type="stmt"/>
        <line num="147" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="148" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="152" count="1" type="stmt"/>
        <line num="154" count="1" type="stmt"/>
        <line num="155" count="1" type="stmt"/>
        <line num="157" count="1" type="stmt"/>
        <line num="158" count="1" type="stmt"/>
        <line num="160" count="1" type="stmt"/>
        <line num="161" count="1" type="cond" truecount="2" falsecount="0"/>
        <line num="163" count="1" type="stmt"/>
        <line num="164" count="1" type="cond" truecount="2" falsecount="0"/>
        <line num="165" count="1" type="stmt"/>
        <line num="169" count="1" type="stmt"/>
        <line num="170" count="1" type="stmt"/>
        <line num="171" count="1" type="stmt"/>
        <line num="172" count="1" type="stmt"/>
        <line num="175" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="177" count="1" type="stmt"/>
        <line num="191" count="1" type="stmt"/>
        <line num="193" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="194" count="0" type="stmt"/>
        <line num="197" count="1" type="stmt"/>
        <line num="199" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="200" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="201" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="202" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="203" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="204" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="206" count="1" type="stmt"/>
        <line num="214" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="215" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="220" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="222" count="0" type="stmt"/>
        <line num="228" count="0" type="stmt"/>
        <line num="229" count="0" type="stmt"/>
        <line num="243" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="244" count="0" type="stmt"/>
        <line num="245" count="0" type="stmt"/>
        <line num="249" count="0" type="stmt"/>
        <line num="250" count="0" type="stmt"/>
        <line num="261" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="262" count="0" type="stmt"/>
        <line num="263" count="0" type="stmt"/>
        <line num="266" count="0" type="stmt"/>
        <line num="269" count="0" type="stmt"/>
        <line num="271" count="0" type="stmt"/>
        <line num="273" count="0" type="stmt"/>
        <line num="277" count="0" type="stmt"/>
        <line num="279" count="0" type="stmt"/>
        <line num="292" count="1" type="stmt"/>
        <line num="305" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="306" count="0" type="stmt"/>
        <line num="307" count="0" type="stmt"/>
        <line num="310" count="1" type="stmt"/>
        <line num="315" count="2" type="stmt"/>
        <line num="317" count="2" type="cond" truecount="1" falsecount="0"/>
        <line num="318" count="1" type="stmt"/>
        <line num="321" count="1" type="stmt"/>
        <line num="326" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="327" count="0" type="stmt"/>
        <line num="328" count="0" type="stmt"/>
        <line num="334" count="3" type="stmt"/>
        <line num="342" count="2" type="cond" truecount="1" falsecount="1"/>
        <line num="345" count="0" type="stmt"/>
        <line num="346" count="2" type="stmt"/>
        <line num="352" count="3" type="stmt"/>
        <line num="364" count="3" type="stmt"/>
        <line num="371" count="1" type="stmt"/>
      </file>
    </package>
    <package name="utils">
      <metrics statements="80" coveredstatements="80" conditionals="31" coveredconditionals="30" methods="17" coveredmethods="17"/>
      <file name="exerciseImages.ts" path="/Users/<USER>/Downloads/my-workout/src/utils/exerciseImages.ts">
        <metrics statements="48" coveredstatements="48" conditionals="21" coveredconditionals="20" methods="6" coveredmethods="6"/>
        <line num="2" count="6" type="stmt"/>
        <line num="10" count="6" type="stmt"/>
        <line num="12" count="10" type="stmt"/>
        <line num="13" count="10" type="stmt"/>
        <line num="14" count="10" type="stmt"/>
        <line num="21" count="6" type="stmt"/>
        <line num="22" count="3" type="stmt"/>
        <line num="25" count="6" type="stmt"/>
        <line num="27" count="28" type="stmt"/>
        <line num="28" count="28" type="stmt"/>
        <line num="37" count="28" type="stmt"/>
        <line num="47" count="28" type="cond" truecount="2" falsecount="0"/>
        <line num="48" count="28" type="stmt"/>
        <line num="49" count="28" type="cond" truecount="1" falsecount="0"/>
        <line num="50" count="9" type="stmt"/>
        <line num="54" count="19" type="stmt"/>
        <line num="55" count="19" type="cond" truecount="2" falsecount="0"/>
        <line num="56" count="3" type="stmt"/>
        <line num="57" count="16" type="cond" truecount="2" falsecount="0"/>
        <line num="58" count="1" type="stmt"/>
        <line num="59" count="15" type="cond" truecount="2" falsecount="0"/>
        <line num="60" count="2" type="stmt"/>
        <line num="61" count="13" type="cond" truecount="2" falsecount="0"/>
        <line num="62" count="3" type="stmt"/>
        <line num="63" count="10" type="cond" truecount="2" falsecount="0"/>
        <line num="64" count="2" type="stmt"/>
        <line num="65" count="8" type="cond" truecount="1" falsecount="0"/>
        <line num="66" count="4" type="stmt"/>
        <line num="69" count="19" type="cond" truecount="1" falsecount="1"/>
        <line num="72" count="6" type="stmt"/>
        <line num="73" count="10" type="stmt"/>
        <line num="76" count="10" type="stmt"/>
        <line num="85" count="10" type="stmt"/>
        <line num="93" count="10" type="cond" truecount="2" falsecount="0"/>
        <line num="96" count="6" type="stmt"/>
        <line num="97" count="17" type="stmt"/>
        <line num="100" count="17" type="stmt"/>
        <line num="107" count="17" type="stmt"/>
        <line num="115" count="17" type="stmt"/>
        <line num="128" count="17" type="stmt"/>
        <line num="129" count="17" type="stmt"/>
        <line num="130" count="92" type="cond" truecount="1" falsecount="0"/>
        <line num="131" count="11" type="stmt"/>
        <line num="132" count="11" type="stmt"/>
        <line num="136" count="6" type="stmt"/>
        <line num="137" count="6" type="cond" truecount="2" falsecount="0"/>
        <line num="141" count="6" type="stmt"/>
        <line num="142" count="1" type="stmt"/>
      </file>
      <file name="storage.ts" path="/Users/<USER>/Downloads/my-workout/src/utils/storage.ts">
        <metrics statements="32" coveredstatements="32" conditionals="10" coveredconditionals="10" methods="11" coveredmethods="11"/>
        <line num="3" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="3" type="stmt"/>
        <line num="7" count="3" type="stmt"/>
        <line num="8" count="3" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="12" count="21" type="stmt"/>
        <line num="13" count="21" type="cond" truecount="2" falsecount="0"/>
        <line num="16" count="1" type="stmt"/>
        <line num="17" count="3" type="stmt"/>
        <line num="18" count="4" type="stmt"/>
        <line num="19" count="3" type="stmt"/>
        <line num="22" count="1" type="stmt"/>
        <line num="23" count="8" type="stmt"/>
        <line num="24" count="8" type="stmt"/>
        <line num="25" count="33" type="stmt"/>
        <line num="26" count="33" type="cond" truecount="2" falsecount="0"/>
        <line num="29" count="20" type="stmt"/>
        <line num="30" count="20" type="stmt"/>
        <line num="32" count="8" type="stmt"/>
        <line num="33" count="8" type="stmt"/>
        <line num="35" count="8" type="stmt"/>
        <line num="36" count="20" type="cond" truecount="2" falsecount="0"/>
        <line num="38" count="20" type="stmt"/>
        <line num="39" count="32" type="cond" truecount="2" falsecount="0"/>
        <line num="40" count="32" type="stmt"/>
        <line num="44" count="8" type="stmt"/>
        <line num="45" count="20" type="stmt"/>
        <line num="46" count="20" type="stmt"/>
        <line num="47" count="20" type="stmt"/>
        <line num="50" count="8" type="cond" truecount="2" falsecount="0"/>
        <line num="52" count="8" type="stmt"/>
      </file>
    </package>
  </project>
</coverage>
