
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for components</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../prettify.css" />
    <link rel="stylesheet" href="../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../index.html">All files</a> components</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">79.35% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>569/717</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">69.25% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>232/335</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">71.25% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>176/247</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">82.06% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>540/658</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line medium'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file high" data-value="AuthModal.tsx"><a href="AuthModal.tsx.html">AuthModal.tsx</a></td>
	<td data-value="90.9" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 90%"></div><div class="cover-empty" style="width: 10%"></div></div>
	</td>
	<td data-value="90.9" class="pct high">90.9%</td>
	<td data-value="33" class="abs high">30/33</td>
	<td data-value="80.55" class="pct high">80.55%</td>
	<td data-value="36" class="abs high">29/36</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="6" class="abs high">6/6</td>
	<td data-value="90.62" class="pct high">90.62%</td>
	<td data-value="32" class="abs high">29/32</td>
	</tr>

<tr>
	<td class="file high" data-value="Dashboard.tsx"><a href="Dashboard.tsx.html">Dashboard.tsx</a></td>
	<td data-value="92.68" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 92%"></div><div class="cover-empty" style="width: 8%"></div></div>
	</td>
	<td data-value="92.68" class="pct high">92.68%</td>
	<td data-value="41" class="abs high">38/41</td>
	<td data-value="84.21" class="pct high">84.21%</td>
	<td data-value="19" class="abs high">16/19</td>
	<td data-value="78.57" class="pct medium">78.57%</td>
	<td data-value="14" class="abs medium">11/14</td>
	<td data-value="92.5" class="pct high">92.5%</td>
	<td data-value="40" class="abs high">37/40</td>
	</tr>

<tr>
	<td class="file medium" data-value="ExerciseManager.tsx"><a href="ExerciseManager.tsx.html">ExerciseManager.tsx</a></td>
	<td data-value="75.64" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 75%"></div><div class="cover-empty" style="width: 25%"></div></div>
	</td>
	<td data-value="75.64" class="pct medium">75.64%</td>
	<td data-value="78" class="abs medium">59/78</td>
	<td data-value="60" class="pct medium">60%</td>
	<td data-value="20" class="abs medium">12/20</td>
	<td data-value="77.77" class="pct medium">77.77%</td>
	<td data-value="27" class="abs medium">21/27</td>
	<td data-value="74.64" class="pct medium">74.64%</td>
	<td data-value="71" class="abs medium">53/71</td>
	</tr>

<tr>
	<td class="file high" data-value="ExerciseSelector.tsx"><a href="ExerciseSelector.tsx.html">ExerciseSelector.tsx</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="4" class="abs high">4/4</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="1" class="abs high">1/1</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="4" class="abs high">4/4</td>
	</tr>

<tr>
	<td class="file high" data-value="ExercisesByEquipment.tsx"><a href="ExercisesByEquipment.tsx.html">ExercisesByEquipment.tsx</a></td>
	<td data-value="85.86" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 85%"></div><div class="cover-empty" style="width: 15%"></div></div>
	</td>
	<td data-value="85.86" class="pct high">85.86%</td>
	<td data-value="92" class="abs high">79/92</td>
	<td data-value="88" class="pct high">88%</td>
	<td data-value="50" class="abs high">44/50</td>
	<td data-value="76.47" class="pct medium">76.47%</td>
	<td data-value="34" class="abs medium">26/34</td>
	<td data-value="87.5" class="pct high">87.5%</td>
	<td data-value="88" class="abs high">77/88</td>
	</tr>

<tr>
	<td class="file high" data-value="MuscleGroupSelector.tsx"><a href="MuscleGroupSelector.tsx.html">MuscleGroupSelector.tsx</a></td>
	<td data-value="96.49" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 96%"></div><div class="cover-empty" style="width: 4%"></div></div>
	</td>
	<td data-value="96.49" class="pct high">96.49%</td>
	<td data-value="57" class="abs high">55/57</td>
	<td data-value="89.28" class="pct high">89.28%</td>
	<td data-value="28" class="abs high">25/28</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="23" class="abs high">23/23</td>
	<td data-value="96.36" class="pct high">96.36%</td>
	<td data-value="55" class="abs high">53/55</td>
	</tr>

<tr>
	<td class="file high" data-value="PrimaryMuscleSelector.tsx"><a href="PrimaryMuscleSelector.tsx.html">PrimaryMuscleSelector.tsx</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="55" class="abs high">55/55</td>
	<td data-value="85" class="pct high">85%</td>
	<td data-value="20" class="abs high">17/20</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="22" class="abs high">22/22</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="52" class="abs high">52/52</td>
	</tr>

<tr>
	<td class="file high" data-value="SelectedExercisesList.tsx"><a href="SelectedExercisesList.tsx.html">SelectedExercisesList.tsx</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="32" class="abs high">32/32</td>
	<td data-value="80" class="pct high">80%</td>
	<td data-value="10" class="abs high">8/10</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="10" class="abs high">10/10</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="31" class="abs high">31/31</td>
	</tr>

<tr>
	<td class="file high" data-value="WorkoutFlow.tsx"><a href="WorkoutFlow.tsx.html">WorkoutFlow.tsx</a></td>
	<td data-value="97.22" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 97%"></div><div class="cover-empty" style="width: 3%"></div></div>
	</td>
	<td data-value="97.22" class="pct high">97.22%</td>
	<td data-value="36" class="abs high">35/36</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="8" class="abs high">8/8</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="10" class="abs high">10/10</td>
	<td data-value="97.22" class="pct high">97.22%</td>
	<td data-value="36" class="abs high">35/36</td>
	</tr>

<tr>
	<td class="file medium" data-value="WorkoutHistory.tsx"><a href="WorkoutHistory.tsx.html">WorkoutHistory.tsx</a></td>
	<td data-value="65.69" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 65%"></div><div class="cover-empty" style="width: 35%"></div></div>
	</td>
	<td data-value="65.69" class="pct medium">65.69%</td>
	<td data-value="137" class="abs medium">90/137</td>
	<td data-value="33.33" class="pct low">33.33%</td>
	<td data-value="51" class="abs low">17/51</td>
	<td data-value="58" class="pct medium">58%</td>
	<td data-value="50" class="abs medium">29/50</td>
	<td data-value="71.18" class="pct medium">71.18%</td>
	<td data-value="118" class="abs medium">84/118</td>
	</tr>

<tr>
	<td class="file medium" data-value="WorkoutSession.tsx"><a href="WorkoutSession.tsx.html">WorkoutSession.tsx</a></td>
	<td data-value="60.52" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 60%"></div><div class="cover-empty" style="width: 40%"></div></div>
	</td>
	<td data-value="60.52" class="pct medium">60.52%</td>
	<td data-value="152" class="abs medium">92/152</td>
	<td data-value="60.21" class="pct medium">60.21%</td>
	<td data-value="93" class="abs medium">56/93</td>
	<td data-value="34" class="pct low">34%</td>
	<td data-value="50" class="abs low">17/50</td>
	<td data-value="64.88" class="pct medium">64.88%</td>
	<td data-value="131" class="abs medium">85/131</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-08-16T16:52:02.742Z
            </div>
        <script src="../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../sorter.js"></script>
        <script src="../block-navigation.js"></script>
    </body>
</html>
    