
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for services</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../prettify.css" />
    <link rel="stylesheet" href="../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../index.html">All files</a> services</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">59.42% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>246/414</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">46.66% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>77/165</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">72.22% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>65/90</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">59.84% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>231/386</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line medium'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file high" data-value="authService.ts"><a href="authService.ts.html">authService.ts</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="20" class="abs high">20/20</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="9" class="abs high">9/9</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="6" class="abs high">6/6</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="20" class="abs high">20/20</td>
	</tr>

<tr>
	<td class="file high" data-value="exerciseDataService.ts"><a href="exerciseDataService.ts.html">exerciseDataService.ts</a></td>
	<td data-value="88.28" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 88%"></div><div class="cover-empty" style="width: 12%"></div></div>
	</td>
	<td data-value="88.28" class="pct high">88.28%</td>
	<td data-value="111" class="abs high">98/111</td>
	<td data-value="67.44" class="pct medium">67.44%</td>
	<td data-value="43" class="abs medium">29/43</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="25" class="abs high">25/25</td>
	<td data-value="88.46" class="pct high">88.46%</td>
	<td data-value="104" class="abs high">92/104</td>
	</tr>

<tr>
	<td class="file low" data-value="exerciseService.ts"><a href="exerciseService.ts.html">exerciseService.ts</a></td>
	<td data-value="28.04" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 28%"></div><div class="cover-empty" style="width: 72%"></div></div>
	</td>
	<td data-value="28.04" class="pct low">28.04%</td>
	<td data-value="164" class="abs low">46/164</td>
	<td data-value="29.72" class="pct low">29.72%</td>
	<td data-value="74" class="abs low">22/74</td>
	<td data-value="38.7" class="pct low">38.7%</td>
	<td data-value="31" class="abs low">12/31</td>
	<td data-value="28.2" class="pct low">28.2%</td>
	<td data-value="156" class="abs low">44/156</td>
	</tr>

<tr>
	<td class="file medium" data-value="workoutService.ts"><a href="workoutService.ts.html">workoutService.ts</a></td>
	<td data-value="68.9" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 68%"></div><div class="cover-empty" style="width: 32%"></div></div>
	</td>
	<td data-value="68.9" class="pct medium">68.9%</td>
	<td data-value="119" class="abs medium">82/119</td>
	<td data-value="43.58" class="pct low">43.58%</td>
	<td data-value="39" class="abs low">17/39</td>
	<td data-value="78.57" class="pct medium">78.57%</td>
	<td data-value="28" class="abs medium">22/28</td>
	<td data-value="70.75" class="pct medium">70.75%</td>
	<td data-value="106" class="abs medium">75/106</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-08-16T16:52:02.742Z
            </div>
        <script src="../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../sorter.js"></script>
        <script src="../block-navigation.js"></script>
    </body>
</html>
    