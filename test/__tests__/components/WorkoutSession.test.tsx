import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import WorkoutSession from '../../../src/components/WorkoutSession';
import { exerciseDataService } from '../../../src/services/exerciseDataService';
import { workoutService } from '../../../src/services/workoutService';
import { Exercise } from '../../../src/types';

// Mock services
jest.mock('../../../src/services/exerciseDataService');
jest.mock('../../../src/services/workoutService');

// Mock exercise images
jest.mock('../../../src/utils/exerciseImages', () => ({
  getExerciseImage: jest.fn((exerciseId: string) => `/images/${exerciseId}.jpg`),
  getExerciseSpecificFallback: jest.fn(( muscleGroup: string, equipment: string) => 
    `/images/fallback-${muscleGroup}-${equipment}.jpg`
  ),
}));

// Mock lucide-react icons
jest.mock('lucide-react', () => ({
  ArrowLeft: ({ size, className }: any) => <div data-testid="arrow-left-icon" data-size={size} className={className} />,
  Plus: ({ size, className }: any) => <div data-testid="plus-icon" data-size={size} className={className} />,
  Trash2: ({ size, className }: any) => <div data-testid="trash-icon" data-size={size} className={className} />,
  Clock: ({ size, className }: any) => <div data-testid="clock-icon" data-size={size} className={className} />,
  Check: ({ size, className }: any) => <div data-testid="check-icon" data-size={size} className={className} />,
  Weight: ({ size, className }: any) => <div data-testid="weight-icon" data-size={size} className={className} />,
  Hash: ({ size, className }: any) => <div data-testid="hash-icon" data-size={size} className={className} />,
  SkipForward: ({ size, className }: any) => <div data-testid="skip-forward-icon" data-size={size} className={className} />,
  AlertCircle: ({ size, className }: any) => <div data-testid="alert-circle-icon" data-size={size} className={className} />,
  ChevronDown: ({ size, className }: any) => <div data-testid="chevron-down-icon" data-size={size} className={className} />,
  ChevronUp: ({ size, className }: any) => <div data-testid="chevron-up-icon" data-size={size} className={className} />,
  Info: ({ size, className }: any) => <div data-testid="info-icon" data-size={size} className={className} />,
}));

// Mock window.alert
Object.defineProperty(window, 'alert', {
  writable: true,
  value: jest.fn(),
});

// Mock fetch for exercise JSON files
global.fetch = jest.fn();

const mockExerciseDataService = exerciseDataService as jest.Mocked<typeof exerciseDataService>;
const mockWorkoutService = workoutService as jest.Mocked<typeof workoutService>;
const mockAlert = window.alert as jest.MockedFunction<typeof window.alert>;
const mockFetch = global.fetch as jest.MockedFunction<typeof fetch>;

const mockExercises: Exercise[] = [
  {
    id: 'bench-press',
    name: 'Bench Press',
    muscleGroup: 'chest',
    equipment: 'barbell',
    description: 'Chest exercise',
    instructions: [
      'Lie on bench with feet flat on floor',
      'Grip bar with hands slightly wider than shoulders',
      'Lower bar to chest with control',
      'Press bar back to starting position'
    ],
    primaryMuscles: ['chest', 'triceps'],
    secondaryMuscles: ['shoulders'],
    level: 'intermediate',
    mechanic: 'compound',
    force: 'push',
    category: 'strength'
  },
  {
    id: 'push-ups',
    name: 'Push Ups',
    muscleGroup: 'chest',
    equipment: 'bodyweight',
    description: 'Bodyweight chest exercise',
    instructions: [
      'Start in plank position',
      'Lower body until chest nearly touches floor',
      'Push back up to starting position'
    ],
    primaryMuscles: ['chest'],
    secondaryMuscles: ['triceps', 'shoulders'],
    level: 'beginner',
    mechanic: 'compound',
    force: 'push',
    category: 'strength'
  }
];

const mockExerciseDetails = {
  'bench-press': {
    instructions: mockExercises[0].instructions,
    primaryMuscles: mockExercises[0].primaryMuscles,
    secondaryMuscles: mockExercises[0].secondaryMuscles,
    equipment: mockExercises[0].equipment,
    level: mockExercises[0].level,
    mechanic: mockExercises[0].mechanic,
    force: mockExercises[0].force,
    category: mockExercises[0].category
  },
  'push-ups': {
    instructions: mockExercises[1].instructions,
    primaryMuscles: mockExercises[1].primaryMuscles,
    secondaryMuscles: mockExercises[1].secondaryMuscles,
    equipment: mockExercises[1].equipment,
    level: mockExercises[1].level,
    mechanic: mockExercises[1].mechanic,
    force: mockExercises[1].force,
    category: mockExercises[1].category
  }
};

const defaultProps = {
  workoutDate: '2024-01-15',
  startTime: '10:00',
  muscleGroup: 'chest',
  exerciseIds: ['bench-press', 'push-ups'],
  onComplete: jest.fn(),
  onBack: jest.fn()
};

describe('WorkoutSession', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockExerciseDataService.loadAllExercises.mockResolvedValue(mockExercises);
    mockWorkoutService.saveWorkout.mockResolvedValue({
      id: '1',
      date: '2024-01-15',
      startTime: '10:00',
      endTime: '11:00',
      muscleGroup: 'chest',
      exercises: [],
      totalSets: 0,
      totalReps: 0
    });
    
    // Mock fetch responses for exercise details
    mockFetch.mockImplementation((input: string | Request | URL) => {
      const url = typeof input === 'string' ? input : input.toString();
      console.log('Mock fetch called with URL:', url);
      let exerciseId: string;
      if (url.includes('Bench_Press') || url.includes('bench-press')) {
        exerciseId = 'bench-press';
      } else if (url.includes('Push_Ups') || url.includes('push-ups')) {
        exerciseId = 'push-ups';
      } else {
        // Default fallback
        exerciseId = 'bench-press';
      }
      console.log('Returning mock data for exercise:', exerciseId);
      return Promise.resolve({
        ok: true,
        json: () => Promise.resolve(mockExerciseDetails[exerciseId as keyof typeof mockExerciseDetails])
      } as Response);
    });
    
    // Mock Date for consistent timer testing
    jest.useFakeTimers();
    jest.setSystemTime(new Date('2024-01-15T10:00:00'));
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  describe('Loading State', () => {
    it('displays loading spinner while fetching exercises', () => {
      mockExerciseDataService.loadAllExercises.mockImplementation(() => new Promise(() => {})); // Never resolves
      
      render(<WorkoutSession {...defaultProps} />);
      
      expect(screen.getByText('Loading workout...')).toBeInTheDocument();
      expect(document.querySelector('.animate-spin')).toBeInTheDocument(); // Loading spinner
    });
  });

  describe('Exercise Loading', () => {
    it('loads and displays exercises correctly', async () => {
      render(<WorkoutSession {...defaultProps} />);
      
      await waitFor(() => {
        expect(screen.getByText('Bench Press')).toBeInTheDocument();
        expect(screen.getByText('1 / 2')).toBeInTheDocument();
        expect(screen.getByText('Target: chest')).toBeInTheDocument();
        expect(screen.getByText('barbell')).toBeInTheDocument();
      });
    });

    it('handles exercise loading errors gracefully', async () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
      mockExerciseDataService.loadAllExercises.mockRejectedValue(new Error('Failed to load'));
      
      render(<WorkoutSession {...defaultProps} />);
      
      await waitFor(() => {
        expect(consoleSpy).toHaveBeenCalledWith('Error loading exercises:', expect.any(Error));
      });
      
      consoleSpy.mockRestore();
    });

    it('displays no exercises found when exercise list is empty', async () => {
      mockExerciseDataService.loadAllExercises.mockResolvedValue([]);
      
      render(<WorkoutSession {...defaultProps} />);
      
      await waitFor(() => {
        expect(screen.getByText('No exercises found')).toBeInTheDocument();
        expect(screen.getByText('Go Back')).toBeInTheDocument();
      });
    });

    it('calls onBack when Go Back button is clicked in no exercises state', async () => {
      mockExerciseDataService.loadAllExercises.mockResolvedValue([]);
      
      render(<WorkoutSession {...defaultProps} />);
      
      await waitFor(() => {
        const goBackButton = screen.getByText('Go Back');
        fireEvent.click(goBackButton);
      });
      
      expect(defaultProps.onBack).toHaveBeenCalledTimes(1);
    });
  });

  describe('Timer Functionality', () => {
    it('displays and updates workout timer', async () => {
      render(<WorkoutSession {...defaultProps} />);
      
      await waitFor(() => {
        expect(screen.getByText('0:00')).toBeInTheDocument();
      });
      
      // Advance timer by 65 seconds
      jest.advanceTimersByTime(65000);
      
      await waitFor(() => {
        expect(screen.getByText('1:05')).toBeInTheDocument();
      });
    });

    it('handles negative duration gracefully', async () => {
      // Set system time before workout start time
      jest.setSystemTime(new Date('2024-01-15T09:30:00'));
      
      render(<WorkoutSession {...defaultProps} />);
      
      await waitFor(() => {
        expect(screen.getByText('0:00')).toBeInTheDocument();
      });
    });
  });

  describe('Exercise Information Display', () => {
    beforeEach(async () => {
      render(<WorkoutSession {...defaultProps} />);
      await waitFor(() => {
        expect(screen.getByText('Bench Press')).toBeInTheDocument();
      });
    });

    it('displays exercise details correctly', () => {
      expect(screen.getByText('Bench Press')).toBeInTheDocument();
      expect(screen.getByText('barbell')).toBeInTheDocument();
      expect(screen.getByText('Target: chest')).toBeInTheDocument();
      expect(screen.getByText('1 / 2')).toBeInTheDocument();
      expect(screen.getByText('0 sets completed')).toBeInTheDocument();
      expect(screen.getByText('intermediate')).toBeInTheDocument();
    });

    it('expands and collapses instructions section', async () => {
      const instructionsButton = screen.getByText('Instructions');
      
      // Initially collapsed
      expect(screen.queryByText('Lie on bench with feet flat on floor')).not.toBeVisible();
      
      // Expand instructions
      fireEvent.click(instructionsButton);
      
      await waitFor(() => {
        expect(screen.getByText('Lie on bench with feet flat on floor')).toBeVisible();
        expect(screen.getByText('Grip bar with hands slightly wider than shoulders')).toBeVisible();
      });
      
      // Collapse instructions
      fireEvent.click(instructionsButton);
      
      await waitFor(() => {
        expect(screen.queryByText('Lie on bench with feet flat on floor')).not.toBeVisible();
      });
    });

    it('expands and collapses info section', async () => {
      const infoButton = screen.getByText('Info');
      
      // Initially collapsed
      expect(screen.queryByText('Primary Muscles')).not.toBeVisible();
      
      // Expand info
      fireEvent.click(infoButton);
      
      await waitFor(() => {
        expect(screen.getByText('Primary Muscles')).toBeVisible();
        expect(screen.getByText('chest, triceps')).toBeVisible();
        expect(screen.getByText('Secondary Muscles')).toBeVisible();
        expect(screen.getByText('shoulders')).toBeVisible();
        expect(screen.getByText('Level')).toBeVisible();
        expect(screen.getByText('intermediate')).toBeVisible();
      });
      
      // Collapse info
      fireEvent.click(infoButton);
      
      await waitFor(() => {
        expect(screen.queryByText('Primary Muscles')).not.toBeVisible();
      });
    });
  });

  describe('Set Management', () => {
    beforeEach(async () => {
      render(<WorkoutSession {...defaultProps} />);
      await waitFor(() => {
        expect(screen.getByText('Bench Press')).toBeInTheDocument();
      });
    });

    it('allows entering reps and weight', () => {
      const repsInput = screen.getByPlaceholderText('0') as HTMLInputElement;
      const weightInputs = screen.getAllByPlaceholderText('0');
      const weightInput = weightInputs[1] as HTMLInputElement; // Second input is weight
      
      fireEvent.change(repsInput, { target: { value: '10' } });
      fireEvent.change(weightInput, { target: { value: '80' } });
      
      expect(repsInput.value).toBe('10');
      expect(weightInput.value).toBe('80');
    });

    it('disables add set button when reps or weight is 0', () => {
      const addButton = screen.getByText('Add Set');
      expect(addButton).toBeDisabled();
      
      const repsInput = screen.getByPlaceholderText('0');
      fireEvent.change(repsInput, { target: { value: '10' } });
      
      expect(addButton).toBeDisabled(); // Still disabled because weight is 0
    });

    it('enables add set button when both reps and weight are greater than 0', () => {
      const repsInput = screen.getByPlaceholderText('0');
      const weightInputs = screen.getAllByPlaceholderText('0');
      const weightInput = weightInputs[1];
      
      fireEvent.change(repsInput, { target: { value: '10' } });
      fireEvent.change(weightInput, { target: { value: '80' } });
      
      const addButton = screen.getByText('Add Set');
      expect(addButton).not.toBeDisabled();
    });

    it('adds a set and resets input fields', () => {
      const repsInput = screen.getByPlaceholderText('0') as HTMLInputElement;
      const weightInputs = screen.getAllByPlaceholderText('0');
      const weightInput = weightInputs[1] as HTMLInputElement;
      
      fireEvent.change(repsInput, { target: { value: '10' } });
      fireEvent.change(weightInput, { target: { value: '80' } });
      
      const addButton = screen.getByText('Add Set');
      fireEvent.click(addButton);
      
      expect(screen.getByText('Sets')).toBeInTheDocument();
      expect(screen.getByText('Set 1: 10 reps × 80 kg')).toBeInTheDocument();
      expect(screen.getByText('1 sets completed')).toBeInTheDocument();
      
      // Check that inputs are reset
      expect(repsInput.value).toBe('0');
      expect(weightInput.value).toBe('0');
    });

    it('removes a set when delete button is clicked', () => {
      // Add a set first
      const repsInput = screen.getByPlaceholderText('0');
      const weightInputs = screen.getAllByPlaceholderText('0');
      const weightInput = weightInputs[1];
      
      fireEvent.change(repsInput, { target: { value: '10' } });
      fireEvent.change(weightInput, { target: { value: '80' } });
      
      const addButton = screen.getByText('Add Set');
      fireEvent.click(addButton);
      
      expect(screen.getByText('Set 1: 10 reps × 80 kg')).toBeInTheDocument();
      
      // Remove the set
      const deleteButton = screen.getByTestId('trash-icon').closest('button');
      fireEvent.click(deleteButton!);
      
      expect(screen.queryByText('Set 1: 10 reps × 80 kg')).not.toBeInTheDocument();
      expect(screen.getByText('0 sets completed')).toBeInTheDocument();
    });

    it('adds multiple sets correctly', () => {
      const repsInput = screen.getByPlaceholderText('0');
      const weightInputs = screen.getAllByPlaceholderText('0');
      const weightInput = weightInputs[1];
      const addButton = screen.getByText('Add Set');
      
      // Add first set
      fireEvent.change(repsInput, { target: { value: '10' } });
      fireEvent.change(weightInput, { target: { value: '80' } });
      fireEvent.click(addButton);
      
      // Add second set
      fireEvent.change(repsInput, { target: { value: '8' } });
      fireEvent.change(weightInput, { target: { value: '85' } });
      fireEvent.click(addButton);
      
      expect(screen.getByText('Set 1: 10 reps × 80 kg')).toBeInTheDocument();
      expect(screen.getByText('Set 2: 8 reps × 85 kg')).toBeInTheDocument();
      expect(screen.getByText('2 sets completed')).toBeInTheDocument();
    });
  });

  describe('Exercise Completion', () => {
    beforeEach(async () => {
      render(<WorkoutSession {...defaultProps} />);
      await waitFor(() => {
        expect(screen.getByText('Bench Press')).toBeInTheDocument();
      });
    });

    it('shows alert when trying to complete exercise without sets', () => {
      const completeButton = screen.getByText('Complete Exercise');
      fireEvent.click(completeButton);
      
      expect(mockAlert).toHaveBeenCalledWith('Please add at least one set before completing this exercise.');
    });

    it('completes exercise and moves to next exercise', async () => {
      // Add a set
      const repsInput = screen.getByPlaceholderText('0');
      const weightInputs = screen.getAllByPlaceholderText('0');
      const weightInput = weightInputs[1];
      
      fireEvent.change(repsInput, { target: { value: '10' } });
      fireEvent.change(weightInput, { target: { value: '80' } });
      fireEvent.click(screen.getByText('Add Set'));
      
      // Complete exercise
      const completeButton = screen.getByText('Complete Exercise');
      fireEvent.click(completeButton);
      
      await waitFor(() => {
        expect(screen.getByText('Push Ups')).toBeInTheDocument();
        expect(screen.getByText('2 / 2')).toBeInTheDocument();
        expect(screen.getByText('Completed Exercises')).toBeInTheDocument();
        expect(screen.getByText('Bench Press')).toBeInTheDocument();
        expect(screen.getByText('1 sets • Max: 80 kg')).toBeInTheDocument();
      });
    });

    it('shows "Complete Workout" button on last exercise', async () => {
      // Complete first exercise
      const repsInput = screen.getByPlaceholderText('0');
      const weightInputs = screen.getAllByPlaceholderText('0');
      const weightInput = weightInputs[1];
      
      fireEvent.change(repsInput, { target: { value: '10' } });
      fireEvent.change(weightInput, { target: { value: '80' } });
      fireEvent.click(screen.getByText('Add Set'));
      fireEvent.click(screen.getByText('Complete Exercise'));
      
      await waitFor(() => {
        expect(screen.getByText('Push Ups')).toBeInTheDocument();
        expect(screen.getByText('Complete Workout')).toBeInTheDocument();
      });
    });

    it('completes workout and calls onComplete', async () => {
      // Complete first exercise
      let repsInput = screen.getByPlaceholderText('0');
      let weightInputs = screen.getAllByPlaceholderText('0');
      let weightInput = weightInputs[1];
      
      fireEvent.change(repsInput, { target: { value: '10' } });
      fireEvent.change(weightInput, { target: { value: '80' } });
      fireEvent.click(screen.getByText('Add Set'));
      fireEvent.click(screen.getByText('Complete Exercise'));
      
      await waitFor(() => {
        expect(screen.getByText('Push Ups')).toBeInTheDocument();
      });
      
      // Complete second exercise
      repsInput = screen.getByPlaceholderText('0');
      weightInputs = screen.getAllByPlaceholderText('0');
      weightInput = weightInputs[1];
      
      fireEvent.change(repsInput, { target: { value: '15' } });
      fireEvent.change(weightInput, { target: { value: '0' } });
      fireEvent.click(screen.getByText('Add Set'));
      fireEvent.click(screen.getByText('Complete Workout'));
      
      await waitFor(() => {
        expect(mockWorkoutService.saveWorkout).toHaveBeenCalledWith(expect.objectContaining({
          date: '2024-01-15',
          startTime: '10:00',
          muscleGroup: 'chest',
          totalSets: 2,
          totalReps: 25
        }));
        expect(defaultProps.onComplete).toHaveBeenCalledTimes(1);
      });
    });
  });

  describe('Exercise Skipping', () => {
    beforeEach(async () => {
      render(<WorkoutSession {...defaultProps} />);
      await waitFor(() => {
        expect(screen.getByText('Bench Press')).toBeInTheDocument();
      });
    });

    it('shows skip confirmation modal when skip button is clicked', () => {
      const skipButton = screen.getByText('Skip Exercise');
      fireEvent.click(skipButton);
      
      expect(screen.getByText('Skip Exercise?')).toBeInTheDocument();
      expect(screen.getByText('Are you sure you want to skip "Bench Press"?')).toBeInTheDocument();
    });

    it('cancels skip when cancel button is clicked', () => {
      const skipButton = screen.getByText('Skip Exercise');
      fireEvent.click(skipButton);
      
      const cancelButton = screen.getByText('Cancel');
      fireEvent.click(cancelButton);
      
      expect(screen.queryByText('Skip Exercise?')).not.toBeInTheDocument();
    });

    it('skips exercise and moves to next exercise', async () => {
      const skipButton = screen.getByText('Skip Exercise');
      fireEvent.click(skipButton);
      
      const confirmSkipButton = screen.getAllByText('Skip Exercise')[1]; // Second one is in modal
      fireEvent.click(confirmSkipButton);
      
      await waitFor(() => {
        expect(screen.getByText('Push Ups')).toBeInTheDocument();
        expect(screen.getByText('2 / 2')).toBeInTheDocument();
        expect(screen.queryByText('Completed Exercises')).not.toBeInTheDocument();
      });
    });

    it('shows finish confirmation when skipping last exercise', async () => {
      // Skip to last exercise
      const skipButton = screen.getByText('Skip Exercise');
      fireEvent.click(skipButton);
      fireEvent.click(screen.getAllByText('Skip Exercise')[1]);
      
      await waitFor(() => {
        expect(screen.getByText('Push Ups')).toBeInTheDocument();
      });
      
      // Skip last exercise
      const lastSkipButton = screen.getByText('Skip Exercise');
      fireEvent.click(lastSkipButton);
      
      expect(screen.getByText('Finish Workout Early?')).toBeInTheDocument();
    });
  });

  describe('Early Workout Completion', () => {
    beforeEach(async () => {
      render(<WorkoutSession {...defaultProps} />);
      await waitFor(() => {
        expect(screen.getByText('Bench Press')).toBeInTheDocument();
      });
      
      // Complete first exercise to enable "Finish Early" button
      const repsInput = screen.getByPlaceholderText('0');
      const weightInputs = screen.getAllByPlaceholderText('0');
      const weightInput = weightInputs[1];
      
      fireEvent.change(repsInput, { target: { value: '10' } });
      fireEvent.change(weightInput, { target: { value: '80' } });
      fireEvent.click(screen.getByText('Add Set'));
      fireEvent.click(screen.getByText('Complete Exercise'));
    });

    it('shows finish early button after completing at least one exercise', async () => {
      await waitFor(() => {
        expect(screen.getByText('Finish Early')).toBeInTheDocument();
      });
    });

    it('shows finish early confirmation modal', async () => {
      await waitFor(() => {
        const finishEarlyButton = screen.getByText('Finish Early');
        fireEvent.click(finishEarlyButton);
      });
      
      expect(screen.getByText('Finish Workout Early?')).toBeInTheDocument();
      expect(screen.getByText('You\'ve completed 1 out of 2 exercises.')).toBeInTheDocument();
    });

    it('cancels early finish when continue workout is clicked', async () => {
      await waitFor(() => {
        const finishEarlyButton = screen.getByText('Finish Early');
        fireEvent.click(finishEarlyButton);
      });
      
      const continueButton = screen.getByText('Continue Workout');
      fireEvent.click(continueButton);
      
      expect(screen.queryByText('Finish Workout Early?')).not.toBeInTheDocument();
    });

    it('finishes workout early and saves progress', async () => {
      await waitFor(() => {
        const finishEarlyButton = screen.getByText('Finish Early');
        fireEvent.click(finishEarlyButton);
      });
      
      const finishButton = screen.getByText('Finish Workout');
      fireEvent.click(finishButton);
      
      await waitFor(() => {
        expect(mockWorkoutService.saveWorkout).toHaveBeenCalledWith(expect.objectContaining({
          totalSets: 1,
          totalReps: 10
        }));
        expect(defaultProps.onComplete).toHaveBeenCalledTimes(1);
      });
    });

    it('shows alert when trying to finish early with no completed exercises', async () => {
      // Render fresh component without completing any exercises
      render(<WorkoutSession {...defaultProps} />);
      
      await waitFor(() => {
        expect(screen.getByText('Bench Press')).toBeInTheDocument();
      });
      
      // Try to finish early without completing any exercises
      // Note: The "Finish Early" button won't be visible, but we can test the function directly
      // by simulating the scenario where someone tries to finish with no exercises
      const skipButton = screen.getByText('Skip Exercise');
      fireEvent.click(skipButton);
      fireEvent.click(screen.getAllByText('Skip Exercise')[1]);
      
      await waitFor(() => {
        const skipButton2 = screen.getByText('Skip Exercise');
        fireEvent.click(skipButton2);
      });
      
      const finishButton = screen.getByText('Finish Workout');
      fireEvent.click(finishButton);
      
      expect(mockAlert).toHaveBeenCalledWith('You need to complete at least one exercise to save the workout.');
    });
  });

  describe('Navigation', () => {
    it('calls onBack when back button is clicked', async () => {
      render(<WorkoutSession {...defaultProps} />);
      
      await waitFor(() => {
        const backButton = screen.getByTestId('arrow-left-icon').closest('button');
        fireEvent.click(backButton!);
      });
      
      expect(defaultProps.onBack).toHaveBeenCalledTimes(1);
    });
  });

  describe('Image Handling', () => {
    it('handles image loading errors with fallback', async () => {
      render(<WorkoutSession {...defaultProps} />);
      
      await waitFor(() => {
        const images = screen.getAllByRole('img');
        const exerciseImage = images[0] as HTMLImageElement;
        
        // Simulate image error
        fireEvent.error(exerciseImage);
        
        // Check that fallback image is set
        expect(exerciseImage.src).toContain('fallback-chest-barbell.jpg');
      });
    });
  });

  describe('Error Handling', () => {
    it('handles workout saving errors gracefully', async () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
      mockWorkoutService.saveWorkout.mockRejectedValue(new Error('Save failed'));
      
      render(<WorkoutSession {...defaultProps} />);
      
      await waitFor(() => {
        expect(screen.getByText('Bench Press')).toBeInTheDocument();
      });
      
      // Add a set and complete exercise
      const repsInput = screen.getByPlaceholderText('0');
      const weightInputs = screen.getAllByPlaceholderText('0');
      const weightInput = weightInputs[1];
      
      fireEvent.change(repsInput, { target: { value: '10' } });
      fireEvent.change(weightInput, { target: { value: '80' } });
      fireEvent.click(screen.getByText('Add Set'));
      fireEvent.click(screen.getByText('Complete Exercise'));
      
      await waitFor(() => {
        expect(screen.getByText('Push Ups')).toBeInTheDocument();
      });
      
      // Complete second exercise
      const repsInput2 = screen.getByPlaceholderText('0');
      const weightInputs2 = screen.getAllByPlaceholderText('0');
      const weightInput2 = weightInputs2[1];
      
      fireEvent.change(repsInput2, { target: { value: '15' } });
      fireEvent.change(weightInput2, { target: { value: '0' } });
      fireEvent.click(screen.getByText('Add Set'));
      fireEvent.click(screen.getByText('Complete Workout'));
      
      await waitFor(() => {
        expect(consoleSpy).toHaveBeenCalledWith('Error saving workout:', expect.any(Error));
        expect(defaultProps.onComplete).toHaveBeenCalledTimes(1); // Still calls onComplete
      });
      
      consoleSpy.mockRestore();
    });

    it('handles fetch errors for exercise details gracefully', async () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
      mockFetch.mockRejectedValue(new Error('Fetch failed'));
      
      render(<WorkoutSession {...defaultProps} />);
      
      await waitFor(() => {
        expect(screen.getByText('Bench Press')).toBeInTheDocument();
      });
      
      expect(consoleSpy).toHaveBeenCalledWith('Error loading details for bench-press:', expect.any(Error));
      
      consoleSpy.mockRestore();
    });
  });

  describe('Accessibility', () => {
    beforeEach(async () => {
      // Use real timers for async operations
      jest.useRealTimers();
      render(<WorkoutSession {...defaultProps} />);
      // Wait for the exercise to be displayed - check for any occurrence
      await waitFor(() => {
        expect(screen.getAllByText('Bench Press').length).toBeGreaterThan(0);
      }, { timeout: 10000 });
      // Restore fake timers
      jest.useFakeTimers();
      jest.setSystemTime(new Date('2024-01-15T10:00:00'));
    });

    it('has proper form labels', () => {
      // Debug: log what's actually rendered
      console.log('=== DEBUGGING COMPONENT STATE ===');
      console.log('Looking for Reps input...');
      const repsInputs = screen.queryAllByLabelText(/reps/i);
      console.log('Found reps inputs:', repsInputs.length);
      
      console.log('Looking for Weight input...');
      const weightInputs = screen.queryAllByLabelText(/weight/i);
      console.log('Found weight inputs:', weightInputs.length);
      
      console.log('Looking for Add Set button...');
      const addSetButton = screen.queryByText('Add Set');
      console.log('Found Add Set button:', !!addSetButton);
      
      console.log('Looking for Complete Exercise button...');
      const completeButton = screen.queryByText(/Complete Exercise|Complete Workout/);
      console.log('Found Complete button:', !!completeButton);
      
      screen.debug();
      expect(screen.getByLabelText('Reps')).toBeInTheDocument();
      expect(screen.getByLabelText('Weight (kg)')).toBeInTheDocument();
    });

    it('has accessible button text', () => {
      expect(screen.getByText('Add Set')).toBeInTheDocument();
      expect(screen.getByText('Complete Exercise')).toBeInTheDocument();
      expect(screen.getByText('Skip Exercise')).toBeInTheDocument();
    });

    it('has proper image alt text', () => {
      const exerciseImage = screen.getByAltText('Bench Press demonstration');
      expect(exerciseImage).toBeInTheDocument();
    });
  });
});