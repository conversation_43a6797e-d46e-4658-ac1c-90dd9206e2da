import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import WorkoutFlow from '../../../src/components/WorkoutFlow';

// Mock child components
jest.mock('../../../src/components/PrimaryMuscleSelector', () => {
  return function MockPrimaryMuscleSelector({ onBack, onPrimaryMuscleSelected }: any) {
    return (
      <div data-testid="primary-muscle-selector">
        <button onClick={onBack}>Back to Date</button>
        <button onClick={() => onPrimaryMuscleSelected('chest')}>Select Chest</button>
        <button onClick={() => onPrimaryMuscleSelected('arms')}>Select Arms</button>
      </div>
    );
  };
});

jest.mock('../../../src/components/ExercisesByEquipment', () => {
  return function MockExercisesByEquipment({ primaryMuscle, onBack, onExercisesSelected }: any) {
    return (
      <div data-testid="exercises-by-equipment">
        <span>Primary Muscle: {primaryMuscle}</span>
        <button onClick={onBack}>Back to Muscle</button>
        <button onClick={() => onExercisesSelected(['exercise1', 'exercise2'])}>Select Exercises</button>
      </div>
    );
  };
});

jest.mock('../../../src/components/WorkoutSession', () => {
  return function MockWorkoutSession({ workoutDate, startTime, muscleGroup, exerciseIds, onComplete, onBack }: any) {
    return (
      <div data-testid="workout-session">
        <span>Date: {workoutDate}</span>
        <span>Time: {startTime}</span>
        <span>Muscle: {muscleGroup}</span>
        <span>Exercises: {exerciseIds.join(',')}</span>
        <button onClick={onBack}>Back to Exercises</button>
        <button onClick={onComplete}>Complete Workout</button>
      </div>
    );
  };
});

// Mock lucide-react icons
jest.mock('lucide-react', () => ({
  ArrowLeft: ({ size, className }: any) => <div data-testid="arrow-left-icon" data-size={size} className={className} />,
  Calendar: ({ size, className }: any) => <div data-testid="calendar-icon" data-size={size} className={className} />,
  Target: ({ size, className }: any) => <div data-testid="target-icon" data-size={size} className={className} />,
}));

// Mock Date methods to ensure consistent testing
const mockDate = new Date('2024-01-15T10:30:00');
const originalDate = global.Date;

beforeAll(() => {
  const MockDate = jest.fn(() => mockDate) as any;
  MockDate.now = originalDate.now;
  MockDate.UTC = originalDate.UTC;
  MockDate.parse = originalDate.parse;
  Object.setPrototypeOf(MockDate, originalDate);
  global.Date = MockDate;
});

afterAll(() => {
  global.Date = originalDate;
});

describe('WorkoutFlow', () => {
  const mockOnBack = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Date Step (Initial Step)', () => {
    it('renders the date step by default', () => {
      render(<WorkoutFlow onBack={mockOnBack} />);
      
      expect(screen.getByText('New Workout')).toBeInTheDocument();
      expect(screen.getByText('When are you working out?')).toBeInTheDocument();
      expect(screen.getByText('Set the date and start time for your workout')).toBeInTheDocument();
      expect(screen.getByLabelText('Workout Date')).toBeInTheDocument();
      expect(screen.getByLabelText('Start Time')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /choose primary muscle/i })).toBeInTheDocument();
    });

    it('displays calendar and target icons', () => {
      render(<WorkoutFlow onBack={mockOnBack} />);
      
      expect(screen.getByTestId('calendar-icon')).toBeInTheDocument();
      expect(screen.getByTestId('target-icon')).toBeInTheDocument();
    });

    it('has default date and time values', () => {
      render(<WorkoutFlow onBack={mockOnBack} />);
      
      expect(screen.getByDisplayValue('2024-01-15')).toBeInTheDocument();
      expect(screen.getByDisplayValue('10:30')).toBeInTheDocument();
    });

    it('allows changing workout date', () => {
      render(<WorkoutFlow onBack={mockOnBack} />);
      
      const dateInput = screen.getByDisplayValue('2024-01-15') as HTMLInputElement;
      fireEvent.change(dateInput, { target: { value: '2024-02-20' } });
      
      expect(dateInput.value).toBe('2024-02-20');
    });

    it('allows changing start time', () => {
      render(<WorkoutFlow onBack={mockOnBack} />);
      
      const timeInput = screen.getByDisplayValue('10:30') as HTMLInputElement;
      fireEvent.change(timeInput, { target: { value: '14:45' } });
      
      expect(timeInput.value).toBe('14:45');
    });

    it('calls onBack when back button is clicked', () => {
      render(<WorkoutFlow onBack={mockOnBack} />);
      
      const backButton = screen.getByTestId('arrow-left-icon').closest('button');
      fireEvent.click(backButton!);
      
      expect(mockOnBack).toHaveBeenCalledTimes(1);
    });

    it('navigates to muscle step when "Choose Primary Muscle" is clicked', () => {
      render(<WorkoutFlow onBack={mockOnBack} />);
      
      const nextButton = screen.getByRole('button', { name: /choose primary muscle/i });
      fireEvent.click(nextButton);
      
      expect(screen.getByTestId('primary-muscle-selector')).toBeInTheDocument();
      expect(screen.queryByText('When are you working out?')).not.toBeInTheDocument();
    });
  });

  describe('Muscle Step', () => {
    it('renders PrimaryMuscleSelector component', () => {
      render(<WorkoutFlow onBack={mockOnBack} />);
      
      // Navigate to muscle step
      const nextButton = screen.getByRole('button', { name: /choose primary muscle/i });
      fireEvent.click(nextButton);
      
      expect(screen.getByTestId('primary-muscle-selector')).toBeInTheDocument();
    });

    it('navigates back to date step when back is clicked', () => {
      render(<WorkoutFlow onBack={mockOnBack} />);
      
      // Navigate to muscle step
      const nextButton = screen.getByRole('button', { name: /choose primary muscle/i });
      fireEvent.click(nextButton);
      
      // Click back
      const backButton = screen.getByText('Back to Date');
      fireEvent.click(backButton);
      
      expect(screen.getByText('When are you working out?')).toBeInTheDocument();
      expect(screen.queryByTestId('primary-muscle-selector')).not.toBeInTheDocument();
    });

    it('navigates to exercises step when muscle is selected', () => {
      render(<WorkoutFlow onBack={mockOnBack} />);
      
      // Navigate to muscle step
      const nextButton = screen.getByRole('button', { name: /choose primary muscle/i });
      fireEvent.click(nextButton);
      
      // Select muscle
      const selectChestButton = screen.getByText('Select Chest');
      fireEvent.click(selectChestButton);
      
      expect(screen.getByTestId('exercises-by-equipment')).toBeInTheDocument();
      expect(screen.getByText('Primary Muscle: chest')).toBeInTheDocument();
    });
  });

  describe('Exercises Step', () => {
    beforeEach(() => {
      render(<WorkoutFlow onBack={mockOnBack} />);
      
      // Navigate to exercises step
      const nextButton = screen.getByRole('button', { name: /choose primary muscle/i });
      fireEvent.click(nextButton);
      const selectChestButton = screen.getByText('Select Chest');
      fireEvent.click(selectChestButton);
    });

    it('renders ExercisesByEquipment component with correct props', () => {
      expect(screen.getByTestId('exercises-by-equipment')).toBeInTheDocument();
      expect(screen.getByText('Primary Muscle: chest')).toBeInTheDocument();
    });

    it('navigates back to muscle step when back is clicked', () => {
      const backButton = screen.getByText('Back to Muscle');
      fireEvent.click(backButton);
      
      expect(screen.getByTestId('primary-muscle-selector')).toBeInTheDocument();
      expect(screen.queryByTestId('exercises-by-equipment')).not.toBeInTheDocument();
    });

    it('navigates to workout step when exercises are selected', () => {
      const selectExercisesButton = screen.getByText('Select Exercises');
      fireEvent.click(selectExercisesButton);
      
      expect(screen.getByTestId('workout-session')).toBeInTheDocument();
      expect(screen.getByText('Date: 2024-01-15')).toBeInTheDocument();
      expect(screen.getByText('Time: 10:30')).toBeInTheDocument();
      expect(screen.getByText('Muscle: chest')).toBeInTheDocument();
      expect(screen.getByText('Exercises: exercise1,exercise2')).toBeInTheDocument();
    });
  });

  describe('Workout Step', () => {
    beforeEach(() => {
      render(<WorkoutFlow onBack={mockOnBack} />);
      
      // Navigate to workout step
      const nextButton = screen.getByRole('button', { name: /choose primary muscle/i });
      fireEvent.click(nextButton);
      const selectChestButton = screen.getByText('Select Chest');
      fireEvent.click(selectChestButton);
      const selectExercisesButton = screen.getByText('Select Exercises');
      fireEvent.click(selectExercisesButton);
    });

    it('renders WorkoutSession component with correct props', () => {
      expect(screen.getByTestId('workout-session')).toBeInTheDocument();
      expect(screen.getByText('Date: 2024-01-15')).toBeInTheDocument();
      expect(screen.getByText('Time: 10:30')).toBeInTheDocument();
      expect(screen.getByText('Muscle: chest')).toBeInTheDocument();
      expect(screen.getByText('Exercises: exercise1,exercise2')).toBeInTheDocument();
    });

    it('navigates back to exercises step when back is clicked', () => {
      const backButton = screen.getByText('Back to Exercises');
      fireEvent.click(backButton);
      
      expect(screen.getByTestId('exercises-by-equipment')).toBeInTheDocument();
      expect(screen.queryByTestId('workout-session')).not.toBeInTheDocument();
    });

    it('calls onBack when workout is completed', () => {
      const completeButton = screen.getByText('Complete Workout');
      fireEvent.click(completeButton);
      
      expect(mockOnBack).toHaveBeenCalledTimes(1);
    });
  });

  describe('Edge Cases', () => {
    it('returns null when in exercises step but no primary muscle is selected', () => {
      render(<WorkoutFlow onBack={mockOnBack} />);
      
      // Manually navigate to exercises step without selecting muscle
      const nextButton = screen.getByRole('button', { name: /choose primary muscle/i });
      fireEvent.click(nextButton);
      
      // Force the component to exercises step without muscle selection
      // This tests the conditional rendering logic
      expect(screen.getByTestId('primary-muscle-selector')).toBeInTheDocument();
    });

    it('returns null when in workout step but no primary muscle is selected', () => {
      render(<WorkoutFlow onBack={mockOnBack} />);
      
      // This tests the conditional rendering logic for workout step
      const nextButton = screen.getByRole('button', { name: /choose primary muscle/i });
      fireEvent.click(nextButton);
      
      expect(screen.getByTestId('primary-muscle-selector')).toBeInTheDocument();
    });

    it('maintains state when navigating between steps', () => {
      render(<WorkoutFlow onBack={mockOnBack} />);
      
      // Change date and time
      const dateInput = screen.getByDisplayValue('2024-01-15') as HTMLInputElement;
      const timeInput = screen.getByDisplayValue('10:30') as HTMLInputElement;
      fireEvent.change(dateInput, { target: { value: '2024-03-25' } });
      fireEvent.change(timeInput, { target: { value: '16:00' } });
      
      // Navigate through steps
      const nextButton = screen.getByRole('button', { name: /choose primary muscle/i });
      fireEvent.click(nextButton);
      const selectArmsButton = screen.getByText('Select Arms');
      fireEvent.click(selectArmsButton);
      const selectExercisesButton = screen.getByText('Select Exercises');
      fireEvent.click(selectExercisesButton);
      
      // Verify state is maintained
      expect(screen.getByText('Date: 2024-03-25')).toBeInTheDocument();
      expect(screen.getByText('Time: 16:00')).toBeInTheDocument();
      expect(screen.getByText('Muscle: arms')).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('has proper form labels', () => {
      render(<WorkoutFlow onBack={mockOnBack} />);
      
      expect(screen.getByText('Workout Date')).toBeInTheDocument();
      expect(screen.getByText('Start Time')).toBeInTheDocument();
    });

    it('has accessible button text', () => {
      render(<WorkoutFlow onBack={mockOnBack} />);
      
      expect(screen.getByRole('button', { name: /choose primary muscle/i })).toBeInTheDocument();
    });

    it('maintains focus management during navigation', () => {
      render(<WorkoutFlow onBack={mockOnBack} />);
      
      const nextButton = screen.getByRole('button', { name: /choose primary muscle/i });
      fireEvent.click(nextButton);
      
      // Component should render the next step
      expect(screen.getByTestId('primary-muscle-selector')).toBeInTheDocument();
    });
  });
});