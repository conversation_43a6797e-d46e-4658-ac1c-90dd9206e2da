import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import ExerciseManager from '../../../src/components/ExerciseManager';
import { exerciseService } from '../../../src/services/exerciseService';
import { Exercise } from '../../../src/types';

// Mock the exercise service
jest.mock('../../../src/services/exerciseService', () => ({
  exerciseService: {
    getAllExercises: jest.fn(),
    createCustomExercise: jest.fn(),
    updateCustomExercise: jest.fn(),
    deleteCustomExercise: jest.fn(),
  },
}));

// Mock the exercise images utility
jest.mock('../../../src/utils/exerciseImages', () => ({
  getImageFromData: jest.fn(() => '/mock-image.jpg'),
  getExerciseSpecificFallback: jest.fn(() => '/mock-fallback.jpg'),
}));

const mockExerciseService = exerciseService as jest.Mocked<typeof exerciseService>;

describe('ExerciseManager', () => {
  const mockOnClose = jest.fn();

  const mockExercises: Exercise[] = [
    {
      id: 'exercise-1',
      name: 'Push-ups',
      muscleGroup: 'chest',
      equipment: 'bodyweight',
      description: 'Classic push-up exercise'
    },
    {
      id: 'exercise-2',
      name: 'Bench Press',
      muscleGroup: 'chest',
      equipment: 'barbell',
      description: 'Barbell bench press'
    }
  ];

  beforeEach(() => {
    jest.clearAllMocks();
    mockExerciseService.getAllExercises.mockResolvedValue(mockExercises);
    mockExerciseService.createCustomExercise.mockResolvedValue(mockExercises[0]);
    mockExerciseService.updateCustomExercise.mockResolvedValue(mockExercises[0]);
    mockExerciseService.deleteCustomExercise.mockResolvedValue();
  });

  it('renders the exercise manager with title and buttons', async () => {
    render(<ExerciseManager onClose={mockOnClose} />);
    
    await waitFor(() => {
      expect(screen.queryByText('Loading exercises...')).not.toBeInTheDocument();
    });
    
    expect(screen.getByText('Exercise Manager')).toBeInTheDocument();
    expect(screen.getByText('Close')).toBeInTheDocument();
    expect(screen.getByText('Add Exercise')).toBeInTheDocument();
  });

  it('shows loading state initially', () => {
    render(<ExerciseManager onClose={mockOnClose} />);
    
    expect(screen.getByText('Loading exercises...')).toBeInTheDocument();
  });

  it('loads and displays exercises after loading', async () => {
    render(<ExerciseManager onClose={mockOnClose} />);
    
    await waitFor(() => {
      expect(screen.queryByText('Loading exercises...')).not.toBeInTheDocument();
    });

    expect(mockExerciseService.getAllExercises).toHaveBeenCalled();
    expect(screen.getByText('Push-ups')).toBeInTheDocument();
    expect(screen.getByText('Bench Press')).toBeInTheDocument();
  });

  it('calls onClose when close button is clicked', async () => {
    render(<ExerciseManager onClose={mockOnClose} />);
    
    await waitFor(() => {
      expect(screen.queryByText('Loading exercises...')).not.toBeInTheDocument();
    });
    
    const closeButton = screen.getByText('Close');
    fireEvent.click(closeButton);
    
    expect(mockOnClose).toHaveBeenCalledTimes(1);
  });

  it('shows add exercise form when Add Exercise button is clicked', async () => {
    render(<ExerciseManager onClose={mockOnClose} />);
    
    await waitFor(() => {
      expect(screen.queryByText('Loading exercises...')).not.toBeInTheDocument();
    });

    const addButton = screen.getByText('Add Exercise');
    fireEvent.click(addButton);
    
    await waitFor(() => {
      expect(screen.getByText('Add New Exercise')).toBeInTheDocument();
    });
    
    expect(screen.getByPlaceholderText('Enter exercise name')).toBeInTheDocument();
    expect(screen.getByDisplayValue('chest')).toBeInTheDocument(); // Default muscle group
    expect(screen.getByDisplayValue('dumbbell')).toBeInTheDocument(); // Default equipment
    expect(screen.getByPlaceholderText('Enter exercise description')).toBeInTheDocument();
  });

  it('creates a new exercise when form is submitted', async () => {
    const newExercise = {
      id: 'exercise-3',
      name: 'Deadlifts',
      muscleGroup: 'back' as const,
      equipment: 'barbell',
      description: 'Barbell deadlift exercise'
    };

    mockExerciseService.createCustomExercise.mockResolvedValue(newExercise);

    render(<ExerciseManager onClose={mockOnClose} />);
    
    await waitFor(() => {
      expect(screen.queryByText('Loading exercises...')).not.toBeInTheDocument();
    });

    // Open add form
    const addButton = screen.getByText('Add Exercise');
    fireEvent.click(addButton);
    
    await waitFor(() => {
      expect(screen.getByText('Add New Exercise')).toBeInTheDocument();
    });
    
    // Fill form
    const nameInput = screen.getByPlaceholderText('Enter exercise name');
    const muscleGroupSelect = screen.getByDisplayValue('chest'); // Default value
    const equipmentSelect = screen.getByDisplayValue('dumbbell'); // Default value
    const descriptionTextarea = screen.getByPlaceholderText('Enter exercise description');
    
    fireEvent.change(nameInput, { target: { value: 'Deadlifts' } });
    fireEvent.change(muscleGroupSelect, { target: { value: 'back' } });
    fireEvent.change(equipmentSelect, { target: { value: 'barbell' } });
    fireEvent.change(descriptionTextarea, { target: { value: 'Barbell deadlift exercise' } });
    
    // Submit form
    const saveButton = screen.getByText('Save');
    fireEvent.click(saveButton);
    
    await waitFor(() => {
      expect(mockExerciseService.createCustomExercise).toHaveBeenCalledWith({
        name: 'Deadlifts',
        muscleGroup: 'back',
        equipment: 'barbell',
        description: 'Barbell deadlift exercise'
      });
    });
  });

  it('handles service errors gracefully', async () => {
    mockExerciseService.getAllExercises.mockRejectedValue(new Error('Service error'));
    
    render(<ExerciseManager onClose={mockOnClose} />);
    
    await waitFor(() => {
      expect(screen.queryByText('Loading exercises...')).not.toBeInTheDocument();
    });

    // Should still render the component structure
    expect(screen.getByText('Exercise Manager')).toBeInTheDocument();
    expect(screen.getByText('Add Exercise')).toBeInTheDocument();
  });

  it('displays muscle group and equipment information', async () => {
    render(<ExerciseManager onClose={mockOnClose} />);
    
    await waitFor(() => {
      expect(screen.queryByText('Loading exercises...')).not.toBeInTheDocument();
    });

    // Check that muscle groups are displayed
    expect(screen.getAllByText('chest').length).toBeGreaterThanOrEqual(1);
    
    // Check that equipment types are displayed
    expect(screen.getAllByText('bodyweight').length).toBeGreaterThanOrEqual(1);
    expect(screen.getAllByText('barbell').length).toBeGreaterThanOrEqual(1);
  });

  it('calls getAllExercises service method on component mount', async () => {
    render(<ExerciseManager onClose={mockOnClose} />);
    
    await waitFor(() => {
      expect(mockExerciseService.getAllExercises).toHaveBeenCalledTimes(1);
    });
  });
});