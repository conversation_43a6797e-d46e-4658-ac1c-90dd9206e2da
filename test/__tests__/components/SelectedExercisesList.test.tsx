import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { DragEndEvent } from '@dnd-kit/core';
import SelectedExercisesList from '../../../src/components/SelectedExercisesList';
import { Exercise } from '../../../src/types';

// Mock the utility functions
jest.mock('../../../src/utils/exerciseImages', () => ({
  getImageFromData: jest.fn((id: string) => `/exercises/${id}/0.jpg`),
  getExerciseSpecificFallback: jest.fn(( equipment: string) => 
    `/exercises/fallback-${equipment}/0.jpg`
  ),
}));

// Mock @dnd-kit modules
jest.mock('@dnd-kit/core', () => ({
  DndContext: ({ children, onDragEnd }: any) => {
    // Store the onDragEnd callback for testing
    (global as any).mockOnDragEnd = onDragEnd;
    return <div data-testid="dnd-context">{children}</div>;
  },
  closestCenter: jest.fn(),
  KeyboardSensor: jest.fn(),
  PointerSensor: jest.fn(),
  useSensor: jest.fn(),
  useSensors: jest.fn(() => []),
}));

jest.mock('@dnd-kit/sortable', () => ({
  arrayMove: jest.fn((array: string[], oldIndex: number, newIndex: number) => {
    const result = [...array];
    const [removed] = result.splice(oldIndex, 1);
    result.splice(newIndex, 0, removed);
    return result;
  }),
  SortableContext: ({ children }: any) => <div data-testid="sortable-context">{children}</div>,
  sortableKeyboardCoordinates: jest.fn(),
  verticalListSortingStrategy: jest.fn(),
  useSortable: jest.fn(() => ({
    attributes: { 'data-testid': 'sortable-item' },
    listeners: { onMouseDown: jest.fn() },
    setNodeRef: jest.fn(),
    transform: null,
    transition: null,
    isDragging: false,
  })),
}));

jest.mock('@dnd-kit/utilities', () => ({
  CSS: {
    Transform: {
      toString: jest.fn(() => 'transform: none'),
    },
  },
}));

// Mock Lucide React icons
jest.mock('lucide-react', () => ({
  GripVertical: () => <div data-testid="grip-icon" />,
  X: () => <div data-testid="remove-icon" />,
  Eye: () => <div data-testid="view-icon" />,
}));

const mockExercises: Exercise[] = [
  {
    id: 'exercise-1',
    name: 'Push-ups',
    muscleGroup: 'chest',
    equipment: 'bodyweight',
    description: 'Classic push-up exercise',
    level: 'beginner',
    primaryMuscles: ['chest', 'triceps'],
    secondaryMuscles: ['shoulders'],
  },
  {
    id: 'exercise-2',
    name: 'Barbell Squat',
    muscleGroup: 'legs',
    equipment: 'barbell',
    description: 'Compound leg exercise',
    level: 'intermediate',
    primaryMuscles: ['quadriceps', 'glutes'],
    secondaryMuscles: ['hamstrings', 'calves'],
  },
  {
    id: 'exercise-3',
    name: 'Dumbbell Curl',
    muscleGroup: 'arms',
    equipment: 'dumbbell',
    description: 'Bicep isolation exercise',
    level: 'beginner',
    primaryMuscles: ['biceps'],
  },
];

const defaultProps = {
  selectedExerciseIds: ['exercise-1', 'exercise-2'],
  exercises: mockExercises,
  onReorder: jest.fn(),
  onRemove: jest.fn(),
  onViewDetails: jest.fn(),
};

describe('SelectedExercisesList', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Empty State', () => {
    it('renders nothing when no exercises are selected', () => {
      const { container } = render(
        <SelectedExercisesList
          {...defaultProps}
          selectedExerciseIds={[]}
        />
      );
      expect(container.firstChild).toBeNull();
    });

    it('does not render when selectedExerciseIds is empty array', () => {
      render(
        <SelectedExercisesList
          {...defaultProps}
          selectedExerciseIds={[]}
        />
      );
      expect(screen.queryByText('Selected Exercises')).not.toBeInTheDocument();
    });
  });

  describe('Basic Rendering', () => {
    it('renders the component with selected exercises', () => {
      render(<SelectedExercisesList {...defaultProps} />);
      
      expect(screen.getByText('Selected Exercises (2)')).toBeInTheDocument();
      expect(screen.getByText('Drag to reorder execution sequence')).toBeInTheDocument();
    });

    it('displays correct exercise count in header', () => {
      render(
        <SelectedExercisesList
          {...defaultProps}
          selectedExerciseIds={['exercise-1', 'exercise-2', 'exercise-3']}
        />
      );
      expect(screen.getByText('Selected Exercises (3)')).toBeInTheDocument();
    });

    it('renders DndContext and SortableContext', () => {
      render(<SelectedExercisesList {...defaultProps} />);
      
      expect(screen.getByTestId('dnd-context')).toBeInTheDocument();
      expect(screen.getByTestId('sortable-context')).toBeInTheDocument();
    });
  });

  describe('Exercise Item Rendering', () => {
    it('renders exercise items in correct order', () => {
      render(<SelectedExercisesList {...defaultProps} />);
      
      expect(screen.getByText('Push-ups')).toBeInTheDocument();
      expect(screen.getByText('Barbell Squat')).toBeInTheDocument();
    });

    it('displays exercise order numbers correctly', () => {
      render(<SelectedExercisesList {...defaultProps} />);
      
      const orderNumbers = screen.getAllByText(/^[12]$/);
      expect(orderNumbers).toHaveLength(2);
      expect(orderNumbers[0]).toHaveTextContent('1');
      expect(orderNumbers[1]).toHaveTextContent('2');
    });

    it('renders exercise images with correct src and alt attributes', () => {
      render(<SelectedExercisesList {...defaultProps} />);
      
      const pushUpImage = screen.getByAltText('Push-ups');
      const squatImage = screen.getByAltText('Barbell Squat');
      
      expect(pushUpImage).toHaveAttribute('src', '/exercises/exercise-1/0.jpg');
      expect(squatImage).toHaveAttribute('src', '/exercises/exercise-2/0.jpg');
    });

    it('displays exercise equipment and level badges', () => {
      render(<SelectedExercisesList {...defaultProps} />);
      
      expect(screen.getByText('bodyweight')).toBeInTheDocument();
      expect(screen.getByText('barbell')).toBeInTheDocument();
      expect(screen.getByText('beginner')).toBeInTheDocument();
      expect(screen.getByText('intermediate')).toBeInTheDocument();
    });

    it('renders exercise without level badge when level is not provided', () => {
      const exerciseWithoutLevel = {
        ...mockExercises[0],
        level: undefined,
      };
      
      render(
        <SelectedExercisesList
          {...defaultProps}
          exercises={[exerciseWithoutLevel, mockExercises[1]]}
        />
      );
      
      // Should still render the exercise name and equipment
      expect(screen.getByText('Push-ups')).toBeInTheDocument();
      expect(screen.getByText('bodyweight')).toBeInTheDocument();
    });

    it('renders drag handle, view, and remove buttons for each exercise', () => {
      render(<SelectedExercisesList {...defaultProps} />);
      
      expect(screen.getAllByTestId('grip-icon')).toHaveLength(2);
      expect(screen.getAllByTestId('view-icon')).toHaveLength(2);
      expect(screen.getAllByTestId('remove-icon')).toHaveLength(2);
    });
  });

  describe('Callback Functions', () => {
    it('calls onRemove when remove button is clicked', () => {
      render(<SelectedExercisesList {...defaultProps} />);
      
      const removeButtons = screen.getAllByTitle('Remove from workout');
      fireEvent.click(removeButtons[0]);
      
      expect(defaultProps.onRemove).toHaveBeenCalledWith('exercise-1');
      expect(defaultProps.onRemove).toHaveBeenCalledTimes(1);
    });

    it('calls onViewDetails when view button is clicked', () => {
      render(<SelectedExercisesList {...defaultProps} />);
      
      const viewButtons = screen.getAllByTitle('View details');
      fireEvent.click(viewButtons[1]);
      
      expect(defaultProps.onViewDetails).toHaveBeenCalledWith(mockExercises[1]);
      expect(defaultProps.onViewDetails).toHaveBeenCalledTimes(1);
    });

    it('calls onReorder when drag end event occurs', () => {
      const { arrayMove } = require('@dnd-kit/sortable');
      
      render(<SelectedExercisesList {...defaultProps} />);
      
      // Simulate drag end event
      const mockClientRect: ClientRect = {
        top: 0, left: 0, bottom: 0, right: 0, width: 0, height: 0, x: 0, y: 0,
        toJSON: () => ({})
      };
      
      const dragEndEvent: DragEndEvent = {
        active: { 
          id: 'exercise-1', 
          data: { current: {} }, 
          rect: { current: { initial: mockClientRect, translated: mockClientRect } } 
        },
        over: { 
          id: 'exercise-2', 
          data: { current: {} }, 
          rect: mockClientRect,
          disabled: false 
        },
        activatorEvent: {} as any,
        collisions: null,
        delta: { x: 0, y: 0 },
      };
      
      // Call the mocked onDragEnd function
      (global as any).mockOnDragEnd(dragEndEvent);
      
      expect(arrayMove).toHaveBeenCalledWith(['exercise-1', 'exercise-2'], 0, 1);
      expect(defaultProps.onReorder).toHaveBeenCalledWith(['exercise-2', 'exercise-1']);
    });

    it('does not call onReorder when drag end event has no over target', () => {
      render(<SelectedExercisesList {...defaultProps} />);
      
      const mockClientRect: ClientRect = {
        top: 0, left: 0, bottom: 0, right: 0, width: 0, height: 0, x: 0, y: 0,
        toJSON: () => ({})
      };
      
      const dragEndEvent: DragEndEvent = {
        active: { 
          id: 'exercise-1', 
          data: { current: {} }, 
          rect: { current: { initial: mockClientRect, translated: mockClientRect } } 
        },
        over: null,
        activatorEvent: {} as any,
        collisions: null,
        delta: { x: 0, y: 0 },
      };
      
      (global as any).mockOnDragEnd(dragEndEvent);
      
      expect(defaultProps.onReorder).not.toHaveBeenCalled();
    });

    it('does not call onReorder when active and over are the same', () => {
      render(<SelectedExercisesList {...defaultProps} />);
      
      const mockClientRect: ClientRect = {
        top: 0, left: 0, bottom: 0, right: 0, width: 0, height: 0, x: 0, y: 0,
        toJSON: () => ({})
      };
      
      const dragEndEvent: DragEndEvent = {
        active: { 
          id: 'exercise-1', 
          data: { current: {} }, 
          rect: { current: { initial: mockClientRect, translated: mockClientRect } } 
        },
        over: { 
          id: 'exercise-1', 
          data: { current: {} }, 
          rect: mockClientRect,
          disabled: false 
        },
        activatorEvent: {} as any,
        collisions: null,
        delta: { x: 0, y: 0 },
      };
      
      (global as any).mockOnDragEnd(dragEndEvent);
      
      expect(defaultProps.onReorder).not.toHaveBeenCalled();
    });
  });

  describe('Image Error Handling', () => {
    it('calls getExerciseSpecificFallback when image fails to load', () => {
      const { getExerciseSpecificFallback } = require('../../../src/utils/exerciseImages');
      
      render(<SelectedExercisesList {...defaultProps} />);
      
      const image = screen.getByAltText('Push-ups');
      fireEvent.error(image);
      
      expect(getExerciseSpecificFallback).toHaveBeenCalledWith(
        'exercise-1',
        'chest',
        'bodyweight'
      );
    });

    it('updates image src to fallback when error occurs', () => {
      render(<SelectedExercisesList {...defaultProps} />);
      
      const image = screen.getByAltText('Push-ups') as HTMLImageElement;
      fireEvent.error(image);
      
      expect(image.src).toBe('http://localhost/exercises/fallback-bodyweight/0.jpg');
    });
  });

  describe('Exercise Filtering and Ordering', () => {
    it('filters out exercises that are not in the exercises array', () => {
      render(
        <SelectedExercisesList
          {...defaultProps}
          selectedExerciseIds={['exercise-1', 'non-existent-exercise', 'exercise-2']}
        />
      );
      
      expect(screen.getByText('Push-ups')).toBeInTheDocument();
      expect(screen.getByText('Barbell Squat')).toBeInTheDocument();
      expect(screen.queryByText('Non-existent Exercise')).not.toBeInTheDocument();
    });

    it('maintains the order specified by selectedExerciseIds', () => {
      render(
        <SelectedExercisesList
          {...defaultProps}
          selectedExerciseIds={['exercise-2', 'exercise-1']}
        />
      );
      
      const orderNumbers = screen.getAllByText(/^[12]$/);
      const exerciseNames = screen.getAllByRole('heading', { level: 4 });
      
      expect(orderNumbers[0]).toHaveTextContent('1');
      expect(exerciseNames[0]).toHaveTextContent('Barbell Squat');
      expect(orderNumbers[1]).toHaveTextContent('2');
      expect(exerciseNames[1]).toHaveTextContent('Push-ups');
    });
  });

  describe('Accessibility', () => {
    it('has proper button titles for screen readers', () => {
      render(<SelectedExercisesList {...defaultProps} />);
      
      expect(screen.getAllByTitle('View details')).toHaveLength(2);
      expect(screen.getAllByTitle('Remove from workout')).toHaveLength(2);
    });

    it('renders images with proper alt text', () => {
      render(<SelectedExercisesList {...defaultProps} />);
      
      expect(screen.getByAltText('Push-ups')).toBeInTheDocument();
      expect(screen.getByAltText('Barbell Squat')).toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    it('handles empty exercises array gracefully', () => {
      render(
        <SelectedExercisesList
          {...defaultProps}
          exercises={[]}
        />
      );
      
      expect(screen.getByText('Selected Exercises (2)')).toBeInTheDocument();
      expect(screen.queryByText('Push-ups')).not.toBeInTheDocument();
    });

    it('handles single exercise selection', () => {
      render(
        <SelectedExercisesList
          {...defaultProps}
          selectedExerciseIds={['exercise-1']}
        />
      );
      
      expect(screen.getByText('Selected Exercises (1)')).toBeInTheDocument();
      expect(screen.getByText('1')).toBeInTheDocument();
      expect(screen.getByText('Push-ups')).toBeInTheDocument();
    });

    it('handles exercises with missing optional properties', () => {
      const minimalExercise: Exercise = {
        id: 'minimal-exercise',
        name: 'Minimal Exercise',
        muscleGroup: 'core',
        equipment: 'bodyweight',
        description: 'Basic exercise',
      };
      
      render(
        <SelectedExercisesList
          {...defaultProps}
          selectedExerciseIds={['minimal-exercise']}
          exercises={[minimalExercise]}
        />
      );
      
      expect(screen.getByText('Minimal Exercise')).toBeInTheDocument();
      expect(screen.getByText('bodyweight')).toBeInTheDocument();
      // Should not render level badge since it's not provided
      expect(screen.queryByText('beginner')).not.toBeInTheDocument();
    });
  });
});