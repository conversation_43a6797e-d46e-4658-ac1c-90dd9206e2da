import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import AuthModal from '../../../src/components/AuthModal';
import { authService } from '../../../src/services/authService';

// Mock the auth service
jest.mock('../../../src/services/authService', () => ({
  authService: {
    signUp: jest.fn(),
    signIn: jest.fn(),
  },
}));

const mockAuthService = authService as jest.Mocked<typeof authService>;

describe('AuthModal', () => {
  const mockOnClose = jest.fn();
  const mockOnSuccess = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders sign in form by default', () => {
    render(<AuthModal onClose={mockOnClose} onSuccess={mockOnSuccess} />);
    
    expect(screen.getByText('Sign In')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Email')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Password')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Sign In' })).toBeInTheDocument();
  });

  it('switches to sign up form when toggle is clicked', () => {
    render(<AuthModal onClose={mockOnClose} onSuccess={mockOnSuccess} />);
    
    fireEvent.click(screen.getByText('Sign up'));
    
    expect(screen.getByText('Sign Up')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Sign Up' })).toBeInTheDocument();
  });

  it('calls onClose when close button is clicked', () => {
    render(<AuthModal onClose={mockOnClose} onSuccess={mockOnSuccess} />);
    
    fireEvent.click(screen.getByRole('button', { name: /close/i }));
    
    expect(mockOnClose).toHaveBeenCalledTimes(1);
  });

  it('handles successful sign in', async () => {
    mockAuthService.signIn.mockResolvedValueOnce({ user: { id: '1' }, session: null });
    
    render(<AuthModal onClose={mockOnClose} onSuccess={mockOnSuccess} />);
    
    fireEvent.change(screen.getByPlaceholderText('Email'), {
      target: { value: '<EMAIL>' },
    });
    fireEvent.change(screen.getByPlaceholderText('Password'), {
      target: { value: 'password123' },
    });
    
    fireEvent.click(screen.getByRole('button', { name: 'Sign In' }));
    
    await waitFor(() => {
      expect(mockAuthService.signIn).toHaveBeenCalledWith('<EMAIL>', 'password123');
      expect(mockOnSuccess).toHaveBeenCalledTimes(1);
    });
  });

  it('handles sign in error', async () => {
    const errorMessage = 'Invalid credentials';
    mockAuthService.signIn.mockRejectedValueOnce(new Error(errorMessage));
    
    render(<AuthModal onClose={mockOnClose} onSuccess={mockOnSuccess} />);
    
    fireEvent.change(screen.getByPlaceholderText('Email'), {
      target: { value: '<EMAIL>' },
    });
    fireEvent.change(screen.getByPlaceholderText('Password'), {
      target: { value: 'wrongpassword' },
    });
    
    fireEvent.click(screen.getByRole('button', { name: 'Sign In' }));
    
    await waitFor(() => {
      expect(screen.getByText(errorMessage)).toBeInTheDocument();
      expect(mockOnSuccess).not.toHaveBeenCalled();
    });
  });

  it('handles successful sign up', async () => {
    mockAuthService.signUp.mockResolvedValueOnce({ user: { id: '1' }, session: null });
    
    render(<AuthModal onClose={mockOnClose} onSuccess={mockOnSuccess} />);
    
    // Switch to sign up
    fireEvent.click(screen.getByText('Sign up'));
    
    fireEvent.change(screen.getByPlaceholderText('Email'), {
      target: { value: '<EMAIL>' },
    });
    fireEvent.change(screen.getByPlaceholderText('Password'), {
      target: { value: 'password123' },
    });
    
    fireEvent.click(screen.getByRole('button', { name: 'Sign Up' }));
    
    await waitFor(() => {
      expect(mockAuthService.signUp).toHaveBeenCalledWith('<EMAIL>', 'password123');
      expect(mockOnSuccess).toHaveBeenCalledTimes(1);
    });
  });

  it('shows loading state during authentication', async () => {
    mockAuthService.signIn.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 100)));
    
    render(<AuthModal onClose={mockOnClose} onSuccess={mockOnSuccess} />);
    
    fireEvent.change(screen.getByPlaceholderText('Email'), {
      target: { value: '<EMAIL>' },
    });
    fireEvent.change(screen.getByPlaceholderText('Password'), {
      target: { value: 'password123' },
    });
    
    fireEvent.click(screen.getByRole('button', { name: 'Sign In' }));
    
    expect(screen.getByRole('button', { name: 'Sign In' })).toBeDisabled();
  });

  it('validates email format', () => {
    render(<AuthModal onClose={mockOnClose} onSuccess={mockOnSuccess} />);
    
    fireEvent.change(screen.getByPlaceholderText('Email'), {
      target: { value: 'invalid-email' },
    });
    fireEvent.change(screen.getByPlaceholderText('Password'), {
      target: { value: 'password123' },
    });
    
    fireEvent.click(screen.getByRole('button', { name: 'Sign In' }));
    
    expect(mockAuthService.signIn).not.toHaveBeenCalled();
  });

  it('validates password length', () => {
    render(<AuthModal onClose={mockOnClose} onSuccess={mockOnSuccess} />);
    
    fireEvent.change(screen.getByPlaceholderText('Email'), {
      target: { value: '<EMAIL>' },
    });
    fireEvent.change(screen.getByPlaceholderText('Password'), {
      target: { value: '123' },
    });
    
    fireEvent.click(screen.getByRole('button', { name: 'Sign In' }));
    
    expect(mockAuthService.signIn).not.toHaveBeenCalled();
  });
});