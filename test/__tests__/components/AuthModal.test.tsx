import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import AuthModal from '../../../src/components/AuthModal';
import { authService } from '../../../src/services/authService';

// Mock the auth service
jest.mock('../../../src/services/authService', () => ({
  authService: {
    signUp: jest.fn(),
    signIn: jest.fn(),
  },
}));

const mockAuthService = authService as jest.Mocked<typeof authService>;

describe('AuthModal', () => {
  const mockOnClose = jest.fn();
  const mockOnSuccess = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders sign in form by default', () => {
    render(<AuthModal onClose={mockOnClose} onSuccess={mockOnSuccess} />);
    
    expect(screen.getByRole('heading', { name: 'Sign In' })).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Enter your email')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Enter your password')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Sign In' })).toBeInTheDocument();
  });

  it('switches to sign up form when toggle is clicked', () => {
    render(<AuthModal onClose={mockOnClose} onSuccess={mockOnSuccess} />);
    
    fireEvent.click(screen.getByText("Don't have an account? Sign up"));
    
    expect(screen.getByRole('heading', { name: 'Create Account' })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Create Account' })).toBeInTheDocument();
  });

  it('calls onClose when close button is clicked', () => {
    render(<AuthModal onClose={mockOnClose} onSuccess={mockOnSuccess} />);
    
    // Find the close button by its position (first button in the modal)
    const buttons = screen.getAllByRole('button');
    const closeButton = buttons.find(button => button.className.includes('text-gray-400'));
    
    fireEvent.click(closeButton!);
    
    expect(mockOnClose).toHaveBeenCalledTimes(1);
  });

  it('handles successful sign in', async () => {
    mockAuthService.signIn.mockResolvedValueOnce({ user: { id: '1' }, session: null });
    
    render(<AuthModal onClose={mockOnClose} onSuccess={mockOnSuccess} />);
    
    fireEvent.change(screen.getByPlaceholderText('Enter your email'), {
      target: { value: '<EMAIL>' },
    });
    fireEvent.change(screen.getByPlaceholderText('Enter your password'), {
      target: { value: 'password123' },
    });
    
    fireEvent.click(screen.getByRole('button', { name: 'Sign In' }));
    
    await waitFor(() => {
      expect(mockAuthService.signIn).toHaveBeenCalledWith('<EMAIL>', 'password123');
      expect(mockOnSuccess).toHaveBeenCalledTimes(1);
    });
  });

  it('handles sign in error', async () => {
    const errorMessage = 'Invalid credentials';
    mockAuthService.signIn.mockRejectedValueOnce(new Error(errorMessage));
    
    render(<AuthModal onClose={mockOnClose} onSuccess={mockOnSuccess} />);
    
    fireEvent.change(screen.getByPlaceholderText('Enter your email'), {
      target: { value: '<EMAIL>' },
    });
    fireEvent.change(screen.getByPlaceholderText('Enter your password'), {
      target: { value: 'wrongpassword' },
    });
    
    fireEvent.click(screen.getByRole('button', { name: 'Sign In' }));
    
    await waitFor(() => {
      expect(screen.getByText(errorMessage)).toBeInTheDocument();
      expect(mockOnSuccess).not.toHaveBeenCalled();
    });
  });

  it('handles successful sign up', async () => {
    mockAuthService.signUp.mockResolvedValueOnce({ user: { id: '1' }, session: null });
    
    render(<AuthModal onClose={mockOnClose} onSuccess={mockOnSuccess} />);
    
    // Switch to sign up
    fireEvent.click(screen.getByText("Don't have an account? Sign up"));
    
    fireEvent.change(screen.getByPlaceholderText('Enter your email'), {
      target: { value: '<EMAIL>' },
    });
    fireEvent.change(screen.getByPlaceholderText('Enter your password'), {
      target: { value: 'password123' },
    });
    
    fireEvent.click(screen.getByRole('button', { name: 'Create Account' }));
    
    await waitFor(() => {
      expect(mockAuthService.signUp).toHaveBeenCalledWith('<EMAIL>', 'password123');
      expect(mockOnSuccess).toHaveBeenCalledTimes(1);
    });
  });

  it('shows loading state during authentication', async () => {
    mockAuthService.signIn.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 100)));
    
    render(<AuthModal onClose={mockOnClose} onSuccess={mockOnSuccess} />);
    
    fireEvent.change(screen.getByPlaceholderText('Enter your email'), {
      target: { value: '<EMAIL>' },
    });
    fireEvent.change(screen.getByPlaceholderText('Enter your password'), {
      target: { value: 'password123' },
    });
    
    fireEvent.click(screen.getByRole('button', { name: 'Sign In' }));
    
    // Check that the service was called
    expect(mockAuthService.signIn).toHaveBeenCalledWith('<EMAIL>', 'password123');
  });

  it('handles invalid email format', () => {
    render(<AuthModal onClose={mockOnClose} onSuccess={mockOnSuccess} />);
    
    const emailInput = screen.getByPlaceholderText('Enter your email');
    fireEvent.change(emailInput, {
      target: { value: '<EMAIL>' },
    });
    fireEvent.change(screen.getByPlaceholderText('Enter your password'), {
      target: { value: 'password123' },
    });
    
    fireEvent.click(screen.getByRole('button', { name: 'Sign In' }));
    
    // Service should be called with valid inputs
    expect(mockAuthService.signIn).toHaveBeenCalledWith('<EMAIL>', 'password123');
  });

  it('handles valid password length', () => {
    render(<AuthModal onClose={mockOnClose} onSuccess={mockOnSuccess} />);
    
    fireEvent.change(screen.getByPlaceholderText('Enter your email'), {
      target: { value: '<EMAIL>' },
    });
    const passwordInput = screen.getByPlaceholderText('Enter your password');
    fireEvent.change(passwordInput, {
      target: { value: 'validpassword' },
    });
    
    fireEvent.click(screen.getByRole('button', { name: 'Sign In' }));
    
    // Service should be called with valid inputs
    expect(mockAuthService.signIn).toHaveBeenCalledWith('<EMAIL>', 'validpassword');
  });
});