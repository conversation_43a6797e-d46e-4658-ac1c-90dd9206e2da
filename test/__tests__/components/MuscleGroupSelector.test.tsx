import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import MuscleGroupSelector from '../../../src/components/MuscleGroupSelector';
import { exerciseDataService } from '../../../src/services/exerciseDataService';
import { Exercise } from '../../../src/types';

// Mock the exercise data service
jest.mock('../../../src/services/exerciseDataService', () => ({
  exerciseDataService: {
    getExercisesGroupedByPrimaryMuscleAndEquipment: jest.fn(),
  },
}));

const mockExerciseDataService = exerciseDataService as jest.Mocked<typeof exerciseDataService>;

// Mock exercise data
const mockExerciseData = {
  chest: {
    barbell: [
      { id: '1', name: 'Bench Press', muscleGroup: 'chest', equipment: 'barbell', description: 'Chest exercise', primaryMuscles: ['chest'] },
      { id: '2', name: 'Incline Bench Press', muscleGroup: 'chest', equipment: 'barbell', description: 'Chest exercise', primaryMuscles: ['chest'] },
    ] as Exercise[],
    dumbbell: [
      { id: '3', name: 'Dumbbell Press', muscleGroup: 'chest', equipment: 'dumbbell', description: 'Chest exercise', primaryMuscles: ['chest'] },
    ] as Exercise[],
  },
  biceps: {
    barbell: [
      { id: '4', name: 'Barbell Curl', muscleGroup: 'biceps', equipment: 'barbell', description: 'Biceps exercise', primaryMuscles: ['biceps'] },
    ] as Exercise[],
    dumbbell: [
      { id: '5', name: 'Dumbbell Curl', muscleGroup: 'biceps', equipment: 'dumbbell', description: 'Biceps exercise', primaryMuscles: ['biceps'] },
      { id: '6', name: 'Hammer Curl', muscleGroup: 'biceps', equipment: 'dumbbell', description: 'Biceps exercise', primaryMuscles: ['biceps'] },
    ] as Exercise[],
  },
  quadriceps: {
    bodyweight: [
      { id: '7', name: 'Squats', muscleGroup: 'quadriceps', equipment: 'bodyweight', description: 'Leg exercise', primaryMuscles: ['quadriceps'] },
    ] as Exercise[],
    machine: [
      { id: '8', name: 'Leg Press', muscleGroup: 'quadriceps', equipment: 'machine', description: 'Leg exercise', primaryMuscles: ['quadriceps'] },
    ] as Exercise[],
  },
};

describe('MuscleGroupSelector', () => {
  const mockProps = {
    onBack: jest.fn(),
    onMuscleGroupSelected: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockExerciseDataService.getExercisesGroupedByPrimaryMuscleAndEquipment.mockResolvedValue(mockExerciseData);
  });

  it('renders loading state initially', () => {
    render(<MuscleGroupSelector {...mockProps} />);
    
    expect(screen.getByText('Loading exercise database...')).toBeInTheDocument();
    expect(screen.getByText('This may take a moment')).toBeInTheDocument();
    expect(document.querySelector('.animate-spin')).toBeInTheDocument();
  });

  it('renders muscle groups after loading', async () => {
    render(<MuscleGroupSelector {...mockProps} />);
    
    await waitFor(() => {
      expect(screen.queryByText('Loading exercise database...')).not.toBeInTheDocument();
    });

    expect(screen.getByText('Choose Muscle Group')).toBeInTheDocument();
    expect(screen.getByText('Chest')).toBeInTheDocument();
    expect(screen.getByText('Biceps')).toBeInTheDocument();
    expect(screen.getByText('Quadriceps')).toBeInTheDocument();
  });

  it('calls onBack when back button is clicked', async () => {
    render(<MuscleGroupSelector {...mockProps} />);
    
    await waitFor(() => {
      expect(screen.queryByText('Loading exercise database...')).not.toBeInTheDocument();
    });

    const backButton = screen.getAllByRole('button')[0]; // First button should be back button
    fireEvent.click(backButton);
    
    expect(mockProps.onBack).toHaveBeenCalledTimes(1);
  });

  it('displays exercise statistics correctly', async () => {
    render(<MuscleGroupSelector {...mockProps} />);
    
    await waitFor(() => {
      expect(screen.queryByText('Loading exercise database...')).not.toBeInTheDocument();
    });

    // Check muscle groups count (3: chest, biceps, quadriceps)
    expect(screen.getByText('Muscle Groups')).toBeInTheDocument();
    const muscleGroupsSection = screen.getByText('Muscle Groups').parentElement;
    expect(muscleGroupsSection).toHaveTextContent('3');
    expect(muscleGroupsSection).toHaveTextContent('Muscle Groups');
    
    // Check equipment types count (6: chest-barbell, chest-dumbbell, biceps-barbell, biceps-dumbbell, quadriceps-bodyweight, quadriceps-machine)
    expect(screen.getByText('Equipment Types')).toBeInTheDocument();
    const equipmentSection = screen.getByText('Equipment Types').parentElement;
    expect(equipmentSection).toHaveTextContent('6');
    expect(equipmentSection).toHaveTextContent('Equipment Types');
    
    // Check total exercises count (8 exercises total)
    expect(screen.getByText('Total Exercises')).toBeInTheDocument();
    const totalExercisesSection = screen.getByText('Total Exercises').parentElement;
    expect(totalExercisesSection).toHaveTextContent('8');
    expect(totalExercisesSection).toHaveTextContent('Total Exercises');
  });

  it('expands and collapses muscle groups', async () => {
    render(<MuscleGroupSelector {...mockProps} />);
    
    await waitFor(() => {
      expect(screen.queryByText('Loading exercise database...')).not.toBeInTheDocument();
    });

    // Initially, equipment should not be visible
    expect(screen.queryByText('Barbell')).not.toBeInTheDocument();
    expect(screen.queryByText('Dumbbell')).not.toBeInTheDocument();

    // Click to expand chest muscle group
    const chestButton = screen.getByText('Chest');
    fireEvent.click(chestButton);

    await waitFor(() => {
      expect(screen.getByText('Barbell')).toBeInTheDocument();
      expect(screen.getByText('Dumbbell')).toBeInTheDocument();
    });

    // Click again to collapse
    fireEvent.click(chestButton);

    await waitFor(() => {
      expect(screen.queryByText('Barbell')).not.toBeInTheDocument();
      expect(screen.queryByText('Dumbbell')).not.toBeInTheDocument();
    });
  });

  it('calls onMuscleGroupSelected when equipment is clicked', async () => {
    render(<MuscleGroupSelector {...mockProps} />);
    
    await waitFor(() => {
      expect(screen.queryByText('Loading exercise database...')).not.toBeInTheDocument();
    });

    // Expand chest muscle group
    const chestButton = screen.getByText('Chest');
    fireEvent.click(chestButton);

    await waitFor(() => {
      expect(screen.getByText('Barbell')).toBeInTheDocument();
    });

    // Click on barbell equipment
    const barbellButton = screen.getByText('Barbell');
    fireEvent.click(barbellButton);
    
    expect(mockProps.onMuscleGroupSelected).toHaveBeenCalledWith('chest');
  });

  it('filters muscle groups based on search term', async () => {
    render(<MuscleGroupSelector {...mockProps} />);
    
    await waitFor(() => {
      expect(screen.queryByText('Loading exercise database...')).not.toBeInTheDocument();
    });

    // Initially all muscle groups should be visible
    expect(screen.getByText('Chest')).toBeInTheDocument();
    expect(screen.getByText('Biceps')).toBeInTheDocument();
    expect(screen.getByText('Quadriceps')).toBeInTheDocument();

    // Search for 'chest'
    const searchInput = screen.getByPlaceholderText('Search muscle groups, equipment, or exercises...');
    fireEvent.change(searchInput, { target: { value: 'chest' } });

    await waitFor(() => {
      expect(screen.getByText('Chest')).toBeInTheDocument();
      expect(screen.queryByText('Biceps')).not.toBeInTheDocument();
      expect(screen.queryByText('Quadriceps')).not.toBeInTheDocument();
    });

    // Clear search
    fireEvent.change(searchInput, { target: { value: '' } });

    await waitFor(() => {
      expect(screen.getByText('Chest')).toBeInTheDocument();
      expect(screen.getByText('Biceps')).toBeInTheDocument();
      expect(screen.getByText('Quadriceps')).toBeInTheDocument();
    });
  });

  it('filters by equipment name', async () => {
    render(<MuscleGroupSelector {...mockProps} />);
    
    await waitFor(() => {
      expect(screen.queryByText('Loading exercise database...')).not.toBeInTheDocument();
    });

    // Search for 'barbell'
    const searchInput = screen.getByPlaceholderText('Search muscle groups, equipment, or exercises...');
    fireEvent.change(searchInput, { target: { value: 'barbell' } });

    await waitFor(() => {
      // Should show chest and biceps (both have barbell exercises)
      expect(screen.getByText('Chest')).toBeInTheDocument();
      expect(screen.getByText('Biceps')).toBeInTheDocument();
      // Should not show quadriceps (no barbell exercises)
      expect(screen.queryByText('Quadriceps')).not.toBeInTheDocument();
    });
  });

  it('filters by exercise name', async () => {
    render(<MuscleGroupSelector {...mockProps} />);
    
    await waitFor(() => {
      expect(screen.queryByText('Loading exercise database...')).not.toBeInTheDocument();
    });

    // Search for 'Bench Press'
    const searchInput = screen.getByPlaceholderText('Search muscle groups, equipment, or exercises...');
    fireEvent.change(searchInput, { target: { value: 'Bench Press' } });

    await waitFor(() => {
      // Should only show chest (has Bench Press)
      expect(screen.getByText('Chest')).toBeInTheDocument();
      expect(screen.queryByText('Biceps')).not.toBeInTheDocument();
      expect(screen.queryByText('Quadriceps')).not.toBeInTheDocument();
    });
  });

  it('shows no results message when search yields no matches', async () => {
    render(<MuscleGroupSelector {...mockProps} />);
    
    await waitFor(() => {
      expect(screen.queryByText('Loading exercise database...')).not.toBeInTheDocument();
    });

    // Search for something that doesn't exist
    const searchInput = screen.getByPlaceholderText('Search muscle groups, equipment, or exercises...');
    fireEvent.change(searchInput, { target: { value: 'nonexistent' } });

    await waitFor(() => {
      expect(screen.getByText('No muscle groups found')).toBeInTheDocument();
      expect(screen.getByText('Try adjusting your search terms')).toBeInTheDocument();
    });
  });

  it('displays exercise counts correctly for each muscle group', async () => {
    render(<MuscleGroupSelector {...mockProps} />);
    
    await waitFor(() => {
      expect(screen.queryByText('Loading exercise database...')).not.toBeInTheDocument();
    });

    // Check exercise counts in parentheses - use getAllByText since there are duplicates
    const threeExercisesElements = screen.getAllByText('(3 exercises)');
    expect(threeExercisesElements).toHaveLength(2); // chest and biceps both have 3 exercises
    expect(screen.getByText('(2 exercises)')).toBeInTheDocument(); // quadriceps: 1 bodyweight + 1 machine
  });

  it('shows equipment icons correctly', async () => {
    render(<MuscleGroupSelector {...mockProps} />);
    
    await waitFor(() => {
      expect(screen.queryByText('Loading exercise database...')).not.toBeInTheDocument();
    });

    // Expand chest to see equipment
    const chestButton = screen.getByText('Chest');
    fireEvent.click(chestButton);

    await waitFor(() => {
      // Check that equipment sections are displayed with proper formatting
      expect(screen.getByText('Barbell')).toBeInTheDocument();
      expect(screen.getByText('Dumbbell')).toBeInTheDocument();
      expect(screen.getByText('2 exercises')).toBeInTheDocument(); // barbell section
      expect(screen.getByText('1 exercise')).toBeInTheDocument(); // dumbbell section
    });
  });

  it('shows exercise previews in equipment sections', async () => {
    render(<MuscleGroupSelector {...mockProps} />);
    
    await waitFor(() => {
      expect(screen.queryByText('Loading exercise database...')).not.toBeInTheDocument();
    });

    // Expand chest to see equipment
    const chestButton = screen.getByText('Chest');
    fireEvent.click(chestButton);

    await waitFor(() => {
      // Should show first 2 exercises and "+X more" if applicable
      expect(screen.getByText(/Bench Press, Incline Bench Press/)).toBeInTheDocument();
      expect(screen.getByText(/Dumbbell Press/)).toBeInTheDocument();
    });
  });

  it('handles service errors gracefully', async () => {
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
    mockExerciseDataService.getExercisesGroupedByPrimaryMuscleAndEquipment.mockRejectedValue(
      new Error('Service error')
    );

    render(<MuscleGroupSelector {...mockProps} />);
    
    await waitFor(() => {
      expect(screen.queryByText('Loading exercise database...')).not.toBeInTheDocument();
    });

    // Should show empty state when no data is loaded due to error
    expect(screen.getByText('No muscle groups found')).toBeInTheDocument();
    expect(screen.getByText('Try adjusting your search terms')).toBeInTheDocument();
    expect(consoleSpy).toHaveBeenCalledWith('Error loading exercises:', expect.any(Error));
    
    consoleSpy.mockRestore();
  });

  it('formats muscle group names correctly', async () => {
    const dataWithCamelCase = {
      primaryMuscle: {
        barbell: [
          { id: '1', name: 'Test Exercise', muscleGroup: 'primaryMuscle', equipment: 'barbell', description: 'Test exercise', primaryMuscles: ['primaryMuscle'] },
        ] as Exercise[],
      },
    };
    
    mockExerciseDataService.getExercisesGroupedByPrimaryMuscleAndEquipment.mockResolvedValue(dataWithCamelCase);
    
    render(<MuscleGroupSelector {...mockProps} />);
    
    await waitFor(() => {
      expect(screen.queryByText('Loading exercise database...')).not.toBeInTheDocument();
    });

    // Should format camelCase to "Primary Muscle"
    expect(screen.getByText('Primary Muscle')).toBeInTheDocument();
  });

  it('formats equipment names correctly', async () => {
    render(<MuscleGroupSelector {...mockProps} />);
    
    await waitFor(() => {
      expect(screen.queryByText('Loading exercise database...')).not.toBeInTheDocument();
    });

    // Expand quadriceps to see bodyweight equipment
    const quadricepsButton = screen.getByText('Quadriceps');
    fireEvent.click(quadricepsButton);

    await waitFor(() => {
      expect(screen.getByText('Bodyweight')).toBeInTheDocument();
      expect(screen.getByText('Machine')).toBeInTheDocument();
    });
  });

  it('updates filtered results count in statistics', async () => {
    render(<MuscleGroupSelector {...mockProps} />);
    
    await waitFor(() => {
      expect(screen.queryByText('Loading exercise database...')).not.toBeInTheDocument();
    });

    // Initially should show 3 filtered results
    expect(screen.getByText('Filtered Results')).toBeInTheDocument();
    const filteredSection = screen.getByText('Filtered Results').parentElement;
    expect(filteredSection).toHaveTextContent('3');
    expect(filteredSection).toHaveTextContent('Filtered Results');
    
    // Search to filter results
    const searchInput = screen.getByPlaceholderText('Search muscle groups, equipment, or exercises...');
    fireEvent.change(searchInput, { target: { value: 'chest' } });

    await waitFor(() => {
      // Should update filtered results count to 1
      const updatedFilteredSection = screen.getByText('Filtered Results').parentElement;
      expect(updatedFilteredSection).toHaveTextContent('1');
      expect(updatedFilteredSection).toHaveTextContent('Filtered Results');
    });
  });
});