import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import ExerciseSelector from '../../../src/components/ExerciseSelector';
import ExercisesByEquipment from '../../../src/components/ExercisesByEquipment';

// Mock the ExercisesByEquipment component
jest.mock('../../../src/components/ExercisesByEquipment', () => {
  return jest.fn(() => <div data-testid="exercises-by-equipment">ExercisesByEquipment Component</div>);
});

const MockedExercisesByEquipment = ExercisesByEquipment as jest.MockedFunction<typeof ExercisesByEquipment>;

describe('ExerciseSelector', () => {
  const mockProps = {
    primaryMuscle: 'chest',
    onBack: jest.fn(),
    onExercisesSelected: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders without crashing', () => {
    render(<ExerciseSelector {...mockProps} />);
    expect(screen.getByTestId('exercises-by-equipment')).toBeInTheDocument();
  });

  it('passes all props correctly to ExercisesByEquipment', () => {
    render(<ExerciseSelector {...mockProps} />);
    
    expect(MockedExercisesByEquipment).toHaveBeenCalledWith(
      {
        primaryMuscle: 'chest',
        onBack: mockProps.onBack,
        onExercisesSelected: mockProps.onExercisesSelected,
      },
      {}
    );
  });

  it('passes different primaryMuscle prop correctly', () => {
    const propsWithDifferentMuscle = {
      ...mockProps,
      primaryMuscle: 'legs',
    };
    
    render(<ExerciseSelector {...propsWithDifferentMuscle} />);
    
    expect(MockedExercisesByEquipment).toHaveBeenCalledWith(
      {
        primaryMuscle: 'legs',
        onBack: mockProps.onBack,
        onExercisesSelected: mockProps.onExercisesSelected,
      },
      {}
    );
  });

  it('forwards onBack callback correctly', () => {
    const customOnBack = jest.fn();
    const propsWithCustomOnBack = {
      ...mockProps,
      onBack: customOnBack,
    };
    
    render(<ExerciseSelector {...propsWithCustomOnBack} />);
    
    expect(MockedExercisesByEquipment).toHaveBeenCalledWith(
      {
        primaryMuscle: 'chest',
        onBack: customOnBack,
        onExercisesSelected: mockProps.onExercisesSelected,
      },
      {}
    );
  });

  it('forwards onExercisesSelected callback correctly', () => {
    const customOnExercisesSelected = jest.fn();
    const propsWithCustomCallback = {
      ...mockProps,
      onExercisesSelected: customOnExercisesSelected,
    };
    
    render(<ExerciseSelector {...propsWithCustomCallback} />);
    
    expect(MockedExercisesByEquipment).toHaveBeenCalledWith(
      {
        primaryMuscle: 'chest',
        onBack: mockProps.onBack,
        onExercisesSelected: customOnExercisesSelected,
      },
      {}
    );
  });

  it('renders ExercisesByEquipment component exactly once', () => {
    render(<ExerciseSelector {...mockProps} />);
    
    expect(MockedExercisesByEquipment).toHaveBeenCalledTimes(1);
  });

  it('maintains backward compatibility interface', () => {
    // Test that the component accepts the expected props interface
    const validProps = {
      primaryMuscle: 'shoulders',
      onBack: () => {},
      onExercisesSelected: () => {},
    };
    
    expect(() => {
      render(<ExerciseSelector {...validProps} />);
    }).not.toThrow();
  });

  it('handles different muscle group names', () => {
    const muscleGroups = ['chest', 'back', 'legs', 'shoulders', 'arms', 'core'];
    
    muscleGroups.forEach((muscle) => {
      MockedExercisesByEquipment.mockClear();
      
      render(<ExerciseSelector {...mockProps} primaryMuscle={muscle} />);
      
      expect(MockedExercisesByEquipment).toHaveBeenCalledWith(
        expect.objectContaining({
          primaryMuscle: muscle,
        }),
        {}
      );
    });
  });

  it('acts as a pure wrapper component', () => {
    // Verify that ExerciseSelector doesn't add any additional logic
    // and simply forwards everything to ExercisesByEquipment
    const props1 = {
      primaryMuscle: 'test1',
      onBack: jest.fn(),
      onExercisesSelected: jest.fn(),
    };
    
    const { rerender } = render(<ExerciseSelector {...props1} />);
    
    expect(MockedExercisesByEquipment).toHaveBeenLastCalledWith(
      props1,
      {}
    );
    
    const props2 = {
      primaryMuscle: 'test2',
      onBack: jest.fn(),
      onExercisesSelected: jest.fn(),
    };
    
    rerender(<ExerciseSelector {...props2} />);
    
    expect(MockedExercisesByEquipment).toHaveBeenLastCalledWith(
      props2,
      {}
    );
  });
});