import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import PrimaryMuscleSelector from '../../../src/components/PrimaryMuscleSelector';
import { exerciseDataService } from '../../../src/services/exerciseDataService';
import { Exercise } from '../../../src/types';

// Mock the exercise data service
jest.mock('../../../src/services/exerciseDataService', () => ({
  exerciseDataService: {
    getExercisesGroupedByPrimaryMuscleAndEquipment: jest.fn(),
  },
}));

const mockExerciseDataService = exerciseDataService as jest.Mocked<typeof exerciseDataService>;

// Mock exercise data grouped by primary muscle and equipment
const mockExerciseData = {
  quadriceps: {
    bodyweight: [
      { id: '1', name: 'Squats', muscleGroup: 'quadriceps', equipment: 'bodyweight', description: 'Leg exercise', primaryMuscles: ['quadriceps'] },
      { id: '2', name: 'Lunges', muscleGroup: 'quadriceps', equipment: 'bodyweight', description: 'Leg exercise', primaryMuscles: ['quadriceps'] },
    ] as Exercise[],
    barbell: [
      { id: '3', name: 'Barbell Squat', muscleGroup: 'quadriceps', equipment: 'barbell', description: 'Leg exercise', primaryMuscles: ['quadriceps'] },
    ] as Exercise[],
  },
  hamstrings: {
    bodyweight: [
      { id: '4', name: 'Romanian Deadlift', muscleGroup: 'hamstrings', equipment: 'bodyweight', description: 'Leg exercise', primaryMuscles: ['hamstrings'] },
    ] as Exercise[],
  },
  biceps: {
    dumbbell: [
      { id: '5', name: 'Dumbbell Curl', muscleGroup: 'biceps', equipment: 'dumbbell', description: 'Arm exercise', primaryMuscles: ['biceps'] },
      { id: '6', name: 'Hammer Curl', muscleGroup: 'biceps', equipment: 'dumbbell', description: 'Arm exercise', primaryMuscles: ['biceps'] },
    ] as Exercise[],
  },
  chest: {
    bodyweight: [
      { id: '7', name: 'Push-ups', muscleGroup: 'chest', equipment: 'bodyweight', description: 'Chest exercise', primaryMuscles: ['chest'] },
    ] as Exercise[],
    barbell: [
      { id: '8', name: 'Bench Press', muscleGroup: 'chest', equipment: 'barbell', description: 'Chest exercise', primaryMuscles: ['chest'] },
    ] as Exercise[],
  },
  abdominals: {
    bodyweight: [
      { id: '9', name: 'Crunches', muscleGroup: 'abdominals', equipment: 'bodyweight', description: 'Core exercise', primaryMuscles: ['abdominals'] },
    ] as Exercise[],
  },
  shoulders: {
    dumbbell: [
      { id: '10', name: 'Shoulder Press', muscleGroup: 'shoulders', equipment: 'dumbbell', description: 'Shoulder exercise', primaryMuscles: ['shoulders'] },
    ] as Exercise[],
  },
};

describe('PrimaryMuscleSelector', () => {
  const mockProps = {
    onBack: jest.fn(),
    onPrimaryMuscleSelected: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockExerciseDataService.getExercisesGroupedByPrimaryMuscleAndEquipment.mockResolvedValue(mockExerciseData);
  });

  it('renders loading state initially', () => {
    render(<PrimaryMuscleSelector {...mockProps} />);
    
    expect(screen.getByText('Loading muscle groups...')).toBeInTheDocument();
    expect(screen.getByText('Reading exercise database...')).toBeInTheDocument();
    expect(document.querySelector('.animate-spin')).toBeInTheDocument();
  });

  it('renders muscle group categories after loading', async () => {
    render(<PrimaryMuscleSelector {...mockProps} />);
    
    await waitFor(() => {
      expect(screen.getByText('Choose Primary Muscle Group')).toBeInTheDocument();
    });

    // Check that muscle group categories are rendered
    expect(screen.getByRole('button', { name: /legs/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /arms/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /chest/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /core/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /shoulders/i })).toBeInTheDocument();
  });

  it('calls onBack when back button is clicked', async () => {
    render(<PrimaryMuscleSelector {...mockProps} />);
    
    await waitFor(() => {
      expect(screen.getByText('Choose Primary Muscle Group')).toBeInTheDocument();
    });

    const buttons = screen.getAllByRole('button');
    const backButton = buttons[0]; // First button is the back button
    fireEvent.click(backButton);
    
    expect(mockProps.onBack).toHaveBeenCalledTimes(1);
  });

  it('displays correct statistics', async () => {
    render(<PrimaryMuscleSelector {...mockProps} />);
    
    await waitFor(() => {
      expect(screen.getByText('Choose Primary Muscle Group')).toBeInTheDocument();
    });

    // Check muscle group categories count (should be 5: Legs, Arms, Chest, Core, Shoulders)
    const categoriesElement = screen.getByText('Muscle Group Categories').parentElement;
    expect(categoriesElement).toHaveTextContent('5');

    // Check total exercises count (should be 10 total exercises)
    const totalExercisesElement = screen.getByText('Total Exercises').parentElement;
    expect(totalExercisesElement).toHaveTextContent('10');

    // Check filtered results count (should be 5 initially)
    const filteredResultsElement = screen.getByText('Filtered Results').parentElement;
    expect(filteredResultsElement).toHaveTextContent('5');
  });

  it('calls onPrimaryMuscleSelected when muscle group is clicked', async () => {
    render(<PrimaryMuscleSelector {...mockProps} />);
    
    await waitFor(() => {
      expect(screen.getByText('Choose Primary Muscle Group')).toBeInTheDocument();
    });

    const legsButton = screen.getByRole('button', { name: /legs/i });
    fireEvent.click(legsButton);
    
    expect(mockProps.onPrimaryMuscleSelected).toHaveBeenCalledWith('Legs');
  });

  it('filters muscle groups based on search term', async () => {
    render(<PrimaryMuscleSelector {...mockProps} />);
    
    await waitFor(() => {
      expect(screen.getByText('Choose Primary Muscle Group')).toBeInTheDocument();
    });

    const searchInput = screen.getByPlaceholderText('Search muscle groups...');
    fireEvent.change(searchInput, { target: { value: 'legs' } });

    // Should only show Legs category
    expect(screen.getByRole('button', { name: /legs/i })).toBeInTheDocument();
    expect(screen.queryByRole('button', { name: /arms/i })).not.toBeInTheDocument();
    expect(screen.queryByRole('button', { name: /chest/i })).not.toBeInTheDocument();

    // Check filtered results count
    const filteredResultsElement = screen.getByText('Filtered Results').parentElement;
    expect(filteredResultsElement).toHaveTextContent('1');
  });

  it('filters muscle groups based on individual muscle names', async () => {
    render(<PrimaryMuscleSelector {...mockProps} />);
    
    await waitFor(() => {
      expect(screen.getByText('Choose Primary Muscle Group')).toBeInTheDocument();
    });

    const searchInput = screen.getByPlaceholderText('Search muscle groups...');
    fireEvent.change(searchInput, { target: { value: 'biceps' } });

    // Should only show Arms category (which contains biceps)
    expect(screen.getByRole('button', { name: /arms/i })).toBeInTheDocument();
    expect(screen.queryByRole('button', { name: /legs/i })).not.toBeInTheDocument();
    expect(screen.queryByRole('button', { name: /chest/i })).not.toBeInTheDocument();
  });

  it('shows no results message when search yields no matches', async () => {
    render(<PrimaryMuscleSelector {...mockProps} />);
    
    await waitFor(() => {
      expect(screen.getByText('Choose Primary Muscle Group')).toBeInTheDocument();
    });

    const searchInput = screen.getByPlaceholderText('Search muscle groups...');
    fireEvent.change(searchInput, { target: { value: 'nonexistent' } });

    expect(screen.getByText('No muscle groups found')).toBeInTheDocument();
    expect(screen.getByText('Try adjusting your search terms')).toBeInTheDocument();

    // Check filtered results count is 0
    const filteredResultsElement = screen.getByText('Filtered Results').parentElement;
    expect(filteredResultsElement).toHaveTextContent('0');
  });

  it('displays correct exercise counts for each muscle group', async () => {
    render(<PrimaryMuscleSelector {...mockProps} />);
    
    await waitFor(() => {
      expect(screen.getByText('Choose Primary Muscle Group')).toBeInTheDocument();
    });

    // Legs should have 4 exercises (3 quadriceps + 1 hamstrings)
    expect(screen.getByText('4 exercises available')).toBeInTheDocument();
    
    // Arms and Chest should each have 2 exercises
    expect(screen.getAllByText('2 exercises available')).toHaveLength(2); // Arms and Chest both have 2
    
    // Core and Shoulders should each have 1 exercise
    expect(screen.getAllByText('1 exercise available')).toHaveLength(2); // Core, Shoulders
  });

  it('displays muscle names for each group', async () => {
    render(<PrimaryMuscleSelector {...mockProps} />);
    
    await waitFor(() => {
      expect(screen.getByText('Choose Primary Muscle Group')).toBeInTheDocument();
    });

    // Check that muscle names are displayed for each group
    expect(screen.getByText('Quadriceps, Hamstrings')).toBeInTheDocument(); // Legs
    expect(screen.getAllByText('Biceps')).toHaveLength(1); // Arms
    expect(screen.getAllByText('Chest')).toHaveLength(2); // Chest (appears as group name and muscle name)
    expect(screen.getByText('Abdominals')).toBeInTheDocument(); // Core
    expect(screen.getAllByText('Shoulders')).toHaveLength(2); // Shoulders (appears as group name and muscle name)
  });

  it('shows View Exercises text on muscle group buttons', async () => {
    render(<PrimaryMuscleSelector {...mockProps} />);
    
    await waitFor(() => {
      expect(screen.getByText('Choose Primary Muscle Group')).toBeInTheDocument();
    });

    const viewExercisesTexts = screen.getAllByText('View Exercises');
    expect(viewExercisesTexts).toHaveLength(5); // Should have 5 muscle group categories
  });

  it('handles service error gracefully', async () => {
    const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
    mockExerciseDataService.getExercisesGroupedByPrimaryMuscleAndEquipment.mockRejectedValue(new Error('Service error'));
    
    render(<PrimaryMuscleSelector {...mockProps} />);
    
    await waitFor(() => {
      expect(screen.getByText('Choose Primary Muscle Group')).toBeInTheDocument();
    });

    // Should show empty state when service fails
    expect(screen.getByText('No muscle groups found')).toBeInTheDocument();
    expect(screen.getByText('Try adjusting your search terms')).toBeInTheDocument();
    
    expect(consoleErrorSpy).toHaveBeenCalledWith('Error loading primary muscles:', expect.any(Error));
    consoleErrorSpy.mockRestore();
  });

  it('filters out neck exercises', async () => {
    const dataWithNeck = {
      ...mockExerciseData,
      neck: {
        bodyweight: [
          { id: '11', name: 'Neck Exercise', muscleGroup: 'neck', equipment: 'bodyweight', description: 'Neck exercise', primaryMuscles: ['neck'] },
        ] as Exercise[],
      },
    };
    
    mockExerciseDataService.getExercisesGroupedByPrimaryMuscleAndEquipment.mockResolvedValue(dataWithNeck);
    
    render(<PrimaryMuscleSelector {...mockProps} />);
    
    await waitFor(() => {
      expect(screen.getByText('Choose Primary Muscle Group')).toBeInTheDocument();
    });

    // Should not show neck exercises in any category
    expect(screen.queryByText('Neck')).not.toBeInTheDocument();
    
    // Total exercises should still be 10 (neck exercise filtered out)
    const totalExercisesElement = screen.getByText('Total Exercises').parentElement;
    expect(totalExercisesElement).toHaveTextContent('10');
  });

  it('only shows muscle groups with exercises', async () => {
    const limitedData = {
      quadriceps: {
        bodyweight: [
          { id: '1', name: 'Squats', muscleGroup: 'quadriceps', equipment: 'bodyweight', description: 'Leg exercise', primaryMuscles: ['quadriceps'] },
        ] as Exercise[],
      },
    };
    
    mockExerciseDataService.getExercisesGroupedByPrimaryMuscleAndEquipment.mockResolvedValue(limitedData);
    
    render(<PrimaryMuscleSelector {...mockProps} />);
    
    await waitFor(() => {
      expect(screen.getByText('Choose Primary Muscle Group')).toBeInTheDocument();
    });

    // Should only show Legs category (which has quadriceps)
    expect(screen.getByRole('button', { name: /legs/i })).toBeInTheDocument();
    expect(screen.queryByRole('button', { name: /arms/i })).not.toBeInTheDocument();
    expect(screen.queryByRole('button', { name: /chest/i })).not.toBeInTheDocument();
    expect(screen.queryByRole('button', { name: /core/i })).not.toBeInTheDocument();
    expect(screen.queryByRole('button', { name: /shoulders/i })).not.toBeInTheDocument();

    // Check statistics
    const categoriesElement = screen.getByText('Muscle Group Categories').parentElement;
    expect(categoriesElement).toHaveTextContent('1');
  });

  it('clears search term when typing', async () => {
    render(<PrimaryMuscleSelector {...mockProps} />);
    
    await waitFor(() => {
      expect(screen.getByText('Choose Primary Muscle Group')).toBeInTheDocument();
    });

    const searchInput = screen.getByPlaceholderText('Search muscle groups...') as HTMLInputElement;
    
    fireEvent.change(searchInput, { target: { value: 'legs' } });
    expect(searchInput.value).toBe('legs');
    
    fireEvent.change(searchInput, { target: { value: '' } });
    expect(searchInput.value).toBe('');
    
    // All muscle groups should be visible again
    expect(screen.getByRole('button', { name: /legs/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /arms/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /chest/i })).toBeInTheDocument();
  });
});