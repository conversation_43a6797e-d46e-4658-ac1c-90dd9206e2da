import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import Dashboard from '../../../src/components/Dashboard';
import { workoutService } from '../../../src/services/workoutService';
import { User } from '@supabase/supabase-js';
import { Workout, MonthlyAnalytics } from '../../../src/types';

// Mock the workout service
jest.mock('../../../src/services/workoutService', () => ({
  workoutService: {
    getWorkouts: jest.fn(),
    getMonthlyAnalytics: jest.fn(),
  },
}));

const mockWorkoutService = workoutService as jest.Mocked<typeof workoutService>;

describe('Dashboard', () => {
  const mockUser: User = {
    id: 'user-123',
    email: '<EMAIL>',
    created_at: '2024-01-01T00:00:00Z',
    app_metadata: {},
    user_metadata: {},
    aud: 'authenticated',
    role: 'authenticated',
  };

  const mockWorkouts: Workout[] = [
    {
      id: 'workout-1',
      date: '2024-01-15',
      startTime: '10:00',
      endTime: '11:30',
      muscleGroup: 'chest',
      totalSets: 12,
      totalReps: 120,
      exercises: [],
    },
    {
      id: 'workout-2',
      date: '2024-01-20',
      startTime: '14:00',
      endTime: '15:00',
      muscleGroup: 'legs',
      totalSets: 8,
      totalReps: 80,
      exercises: [],
    },
  ];

  const mockAnalytics: MonthlyAnalytics = {
    month: 'January',
    year: 2024,
    totalWorkouts: 2,
    totalSets: 20,
    totalReps: 200,
    muscleGroups: { chest: 1, legs: 1 },
    exerciseMaxWeights: { 'Bench Press': 155, 'Squats': 205 },
    averageWorkoutDuration: 75,
  };

  const mockProps = {
    user: mockUser,
    onStartWorkout: jest.fn(),
    onViewHistory: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockWorkoutService.getWorkouts.mockResolvedValue(mockWorkouts);
    mockWorkoutService.getMonthlyAnalytics.mockResolvedValue(mockAnalytics);
  });

  it('renders dashboard with user greeting', async () => {
    render(<Dashboard {...mockProps} />);
    
    await waitFor(() => {
      expect(screen.getByText(/Welcome back/)).toBeInTheDocument();
    });
  });

  it('displays quick action buttons', () => {
    render(<Dashboard {...mockProps} />);
    
    expect(screen.getByText('Start Workout')).toBeInTheDocument();
    expect(screen.getByText('View History')).toBeInTheDocument();
  });

  it('calls onStartWorkout when Start Workout button is clicked', () => {
    render(<Dashboard {...mockProps} />);
    
    fireEvent.click(screen.getByText('Start Workout'));
    expect(mockProps.onStartWorkout).toHaveBeenCalledTimes(1);
  });

  it('calls onViewHistory when View History button is clicked', () => {
    render(<Dashboard {...mockProps} />);
    
    fireEvent.click(screen.getByText('View History'));
    expect(mockProps.onViewHistory).toHaveBeenCalledTimes(1);
  });

  it('displays recent workouts section', async () => {
    render(<Dashboard {...mockProps} />);
    
    await waitFor(() => {
      expect(screen.getByText('Recent Workouts')).toBeInTheDocument();
    });
  });

  it('shows recent workout items', async () => {
    render(<Dashboard {...mockProps} />);
    
    await waitFor(() => {
      expect(screen.getByText('chest')).toBeInTheDocument();
      expect(screen.getByText('legs')).toBeInTheDocument();
    });
  });

  it('displays monthly analytics section', async () => {
    render(<Dashboard {...mockProps} />);
    
    await waitFor(() => {
      expect(screen.getByText('This Month')).toBeInTheDocument();
    });
  });

  it('shows analytics data', async () => {
    render(<Dashboard {...mockProps} />);
    
    await waitFor(() => {
      expect(screen.getByText('2')).toBeInTheDocument(); // total workouts
      expect(screen.getByText('20')).toBeInTheDocument(); // total sets
      expect(screen.getByText('200')).toBeInTheDocument(); // total reps
    });
  });

  it('handles loading state', () => {
    mockWorkoutService.getWorkouts.mockImplementation(() => new Promise(() => {})); // Never resolves
    
    render(<Dashboard {...mockProps} />);
    
    // Should show loading indicators or skeleton content
    expect(screen.getByText('Welcome back')).toBeInTheDocument();
  });

  it('handles error when fetching workouts fails', async () => {
    mockWorkoutService.getWorkouts.mockRejectedValue(new Error('Failed to fetch'));
    
    render(<Dashboard {...mockProps} />);
    
    // Component should still render without crashing
    await waitFor(() => {
      expect(screen.getByText('Welcome back')).toBeInTheDocument();
    });
  });

  it('handles error when fetching analytics fails', async () => {
    mockWorkoutService.getMonthlyAnalytics.mockRejectedValue(new Error('Failed to fetch analytics'));
    
    render(<Dashboard {...mockProps} />);
    
    // Component should still render without crashing
    await waitFor(() => {
      expect(screen.getByText('Welcome back')).toBeInTheDocument();
    });
  });

  it('displays empty state when no workouts exist', async () => {
    mockWorkoutService.getWorkouts.mockResolvedValue([]);
    
    render(<Dashboard {...mockProps} />);
    
    await waitFor(() => {
      expect(screen.getByText('Welcome back')).toBeInTheDocument();
    });
  });

  it('fetches data on component mount', () => {
    render(<Dashboard {...mockProps} />);
    
    expect(mockWorkoutService.getWorkouts).toHaveBeenCalledTimes(1);
    expect(mockWorkoutService.getMonthlyAnalytics).toHaveBeenCalledTimes(1);
  });

  it('displays muscle group distribution', async () => {
    render(<Dashboard {...mockProps} />);
    
    await waitFor(() => {
      // Should show muscle groups from analytics
      expect(screen.getByText(/chest/i)).toBeInTheDocument();
      expect(screen.getByText(/legs/i)).toBeInTheDocument();
    });
  });

  it('shows workout duration information', async () => {
    render(<Dashboard {...mockProps} />);
    
    await waitFor(() => {
      // Should display average workout duration
      expect(screen.getByText(/75/)).toBeInTheDocument(); // 75 minutes
    });
  });

  it('handles month navigation if present', async () => {
    render(<Dashboard {...mockProps} />);
    
    await waitFor(() => {
      expect(screen.getByText('This Month')).toBeInTheDocument();
    });

    // Look for month navigation buttons if they exist
    const prevButton = screen.queryByRole('button', { name: /previous/i });
    const nextButton = screen.queryByRole('button', { name: /next/i });
    
    if (prevButton) {
      fireEvent.click(prevButton);
      // Should call analytics for previous month
    }
    
    if (nextButton) {
      fireEvent.click(nextButton);
      // Should call analytics for next month
    }
  });

  it('displays exercise max weights', async () => {
    render(<Dashboard {...mockProps} />);
    
    await waitFor(() => {
      // Should show max weights from analytics
      expect(screen.getByText(/155/)).toBeInTheDocument(); // Bench Press max
      expect(screen.getByText(/205/)).toBeInTheDocument(); // Squats max
    });
  });

  it('handles expandable sections', async () => {
    render(<Dashboard {...mockProps} />);
    
    await waitFor(() => {
      expect(screen.getByText('Recent Workouts')).toBeInTheDocument();
    });

    // Look for expand/collapse buttons
    const expandButtons = screen.queryAllByRole('button', { name: /expand|collapse|chevron/i });
    
    if (expandButtons.length > 0) {
      fireEvent.click(expandButtons[0]);
      // Should toggle section visibility
    }
  });

  it('renders without user (guest mode)', () => {
    const propsWithoutUser = {
      ...mockProps,
      user: null,
    };
    
    render(<Dashboard {...propsWithoutUser} />);
    
    // Should still render basic dashboard
    expect(screen.getByText('Start Workout')).toBeInTheDocument();
  });

  it('updates analytics when month changes', async () => {
    render(<Dashboard {...mockProps} />);
    
    await waitFor(() => {
      expect(mockWorkoutService.getMonthlyAnalytics).toHaveBeenCalledTimes(1);
    });

    // If there's month navigation, test it
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth();
    const currentYear = currentDate.getFullYear();
    
    expect(mockWorkoutService.getMonthlyAnalytics).toHaveBeenCalledWith(currentMonth, currentYear);
  });
});