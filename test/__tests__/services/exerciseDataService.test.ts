import { exerciseDataService } from '../../../src/services/exerciseDataService';
import { Exercise } from '../../../src/types';

// Mock fetch
global.fetch = jest.fn();
const mockFetch = global.fetch as jest.MockedFunction<typeof fetch>;

describe('exerciseDataService', () => {
  const mockRawExerciseData = {
    id: 'push-ups',
    name: 'Push-ups',
    primaryMuscles: ['chest'],
    secondaryMuscles: ['triceps', 'shoulders'],
    instructions: ['Get into plank position', 'Lower body', 'Push up'],
    category: 'strength',
    equipment: 'body only',
    level: 'beginner',
    force: 'push',
    mechanic: 'compound',
    images: ['push-ups.jpg'],
  };

  const mockExercise: Exercise = {
    id: 'push-ups',
    name: 'Push-ups',
    muscleGroup: 'chest',
    equipment: 'bodyweight',
    description: 'Classic push-up exercise',
    primaryMuscles: ['chest'],
    secondaryMuscles: ['triceps', 'shoulders'],
    instructions: ['Get into plank position', 'Lower body', 'Push up'],
    category: 'strength',
    level: 'beginner',
    mechanic: 'compound',
    force: 'push',
  };

  const mockExerciseManifest = {
    totalFiles: 3,
    files: [
      'push-ups.json',
      'squats.json',
      'bench-press.json',
    ],
  };

  beforeEach(() => {
    jest.clearAllMocks();
    // Clear the cache
    exerciseDataService.clearCache();
  });

  describe('loadAllExercises', () => {
    it('loads all exercises from JSON files', async () => {
      // Mock manifest fetch
      const manifestResponse = {
        ok: true,
        json: jest.fn().mockResolvedValue(mockExerciseManifest),
      };
      
      // Mock exercise file fetches
      const exerciseResponse = {
        ok: true,
        json: jest.fn().mockResolvedValue(mockRawExerciseData),
      };

      mockFetch
        .mockResolvedValueOnce(manifestResponse as any) // manifest
        .mockResolvedValue(exerciseResponse as any); // exercise files

      const result = await exerciseDataService.loadAllExercises();

      expect(result).toHaveLength(3); // Three exercises from manifest
      expect(result[0]).toEqual(expect.objectContaining({
        name: 'Push-ups',
        muscleGroup: 'chest',
        equipment: 'bodyweight', // mapped from 'body only'
      }));
      expect(mockFetch).toHaveBeenCalledTimes(4); // 1 manifest + 3 exercises
    });

    it('handles fetch errors gracefully', async () => {
      mockFetch.mockRejectedValue(new Error('Network error'));

      const result = await exerciseDataService.loadAllExercises();

      expect(result).toEqual([]);
    });

    it('uses cached exercises on subsequent calls', async () => {
      // First call
      const manifestResponse = {
        ok: true,
        json: jest.fn().mockResolvedValue({ totalFiles: 1, files: ['push-ups.json'] }),
      };
      
      const exerciseResponse = {
        ok: true,
        json: jest.fn().mockResolvedValue(mockRawExerciseData),
      };

      mockFetch
        .mockResolvedValueOnce(manifestResponse as any)
        .mockResolvedValueOnce(exerciseResponse as any);

      const result1 = await exerciseDataService.loadAllExercises();
      
      // Second call should use cache
      const result2 = await exerciseDataService.loadAllExercises();

      expect(result1).toEqual(result2);
      expect(mockFetch).toHaveBeenCalledTimes(2); // Only called for first load
    });

    it('handles invalid exercise data', async () => {
      const manifestResponse = {
        ok: true,
        json: jest.fn().mockResolvedValue({ totalFiles: 1, files: ['invalid.json'] }),
      };
      
      const invalidExerciseResponse = {
        ok: true,
        json: jest.fn().mockResolvedValue({ name: '', primaryMuscles: [] }), // Invalid data
      };

      mockFetch
        .mockResolvedValueOnce(manifestResponse as any)
        .mockResolvedValueOnce(invalidExerciseResponse as any);

      const result = await exerciseDataService.loadAllExercises();

      expect(result).toEqual([]); // Invalid exercises filtered out
    });
  });

  describe('getExercisesGroupedByPrimaryMuscleAndEquipment', () => {
    it('groups exercises by primary muscle and equipment', async () => {
      const exercises = [
        { ...mockExercise, muscleGroup: 'chest', equipment: 'bodyweight' },
        { ...mockExercise, id: 'bench-press', name: 'Bench Press', muscleGroup: 'chest', equipment: 'barbell' },
        { ...mockExercise, id: 'squats', name: 'Squats', muscleGroup: 'quadriceps', equipment: 'bodyweight' },
      ];

      // Mock loadAllExercises to return our test data
      jest.spyOn(exerciseDataService, 'loadAllExercises').mockResolvedValue(exercises);

      const result = await exerciseDataService.getExercisesGroupedByPrimaryMuscleAndEquipment();

      expect(result).toEqual({
        chest: {
          bodyweight: [expect.objectContaining({ name: 'Push-ups' })],
          barbell: [expect.objectContaining({ name: 'Bench Press' })],
        },
        quadriceps: {
          bodyweight: [expect.objectContaining({ name: 'Squats' })],
        },
      });
    });

    it('uses cached grouped data on subsequent calls', async () => {
      const exercises = [mockExercise];
      const loadAllExercisesSpy = jest.spyOn(exerciseDataService, 'loadAllExercises').mockResolvedValue(exercises);

      // First call
      const result1 = await exerciseDataService.getExercisesGroupedByPrimaryMuscleAndEquipment();
      
      // Second call should use cache
      const result2 = await exerciseDataService.getExercisesGroupedByPrimaryMuscleAndEquipment();

      expect(result1).toEqual(result2);
      expect(loadAllExercisesSpy).toHaveBeenCalledTimes(1); // Only called once
    });
  });

  describe('getExercisesByPrimaryMuscle', () => {
    it('returns exercises grouped by equipment for specific primary muscle', async () => {
      const exercises = [
        { ...mockExercise, muscleGroup: 'chest', equipment: 'bodyweight' },
        { ...mockExercise, id: 'bench-press', name: 'Bench Press', muscleGroup: 'chest', equipment: 'barbell' },
        { ...mockExercise, id: 'squats', name: 'Squats', muscleGroup: 'legs', equipment: 'bodyweight' },
      ];

      jest.spyOn(exerciseDataService, 'loadAllExercises').mockResolvedValue(exercises);

      const result = await exerciseDataService.getExercisesByPrimaryMuscle('chest');

      expect(result).toEqual({
        bodyweight: [expect.objectContaining({ name: 'Push-ups' })],
        barbell: [expect.objectContaining({ name: 'Bench Press' })],
      });
    });

    it('returns empty object for non-existent muscle group', async () => {
      jest.spyOn(exerciseDataService, 'loadAllExercises').mockResolvedValue([mockExercise]);

      const result = await exerciseDataService.getExercisesByPrimaryMuscle('nonexistent');

      expect(result).toEqual({});
    });
  });

  describe('getExercisesByMuscleGroup', () => {
    it('returns exercises for multiple muscle groups', async () => {
      const exercises = [
        { ...mockExercise, muscleGroup: 'chest', equipment: 'bodyweight' },
        { ...mockExercise, id: 'squats', name: 'Squats', muscleGroup: 'legs', equipment: 'bodyweight' },
        { ...mockExercise, id: 'curls', name: 'Curls', muscleGroup: 'biceps', equipment: 'dumbbell' },
      ];

      jest.spyOn(exerciseDataService, 'loadAllExercises').mockResolvedValue(exercises);

      const result = await exerciseDataService.getExercisesByMuscleGroup(['chest', 'legs']);

      expect(result).toEqual({
        bodyweight: [
          expect.objectContaining({ name: 'Push-ups', muscleGroup: 'chest' }),
          expect.objectContaining({ name: 'Squats', muscleGroup: 'legs' }),
        ],
      });
    });

    it('returns empty object for non-existent muscle groups', async () => {
      jest.spyOn(exerciseDataService, 'loadAllExercises').mockResolvedValue([mockExercise]);

      const result = await exerciseDataService.getExercisesByMuscleGroup(['nonexistent']);

      expect(result).toEqual({});
    });
  });

  describe('getAvailablePrimaryMuscles', () => {
    it('returns unique primary muscles from all exercises', async () => {
      const exercises = [
        { ...mockExercise, muscleGroup: 'chest' },
        { ...mockExercise, id: 'squats', muscleGroup: 'legs' },
        { ...mockExercise, id: 'bench-press', muscleGroup: 'chest' }, // duplicate
      ];
      
      jest.spyOn(exerciseDataService, 'loadAllExercises').mockResolvedValue(exercises);

      const result = await exerciseDataService.getAvailablePrimaryMuscles();

      expect(result).toEqual(['chest', 'legs']);
      expect(result).toHaveLength(2); // No duplicates
    });

    it('returns empty array when no exercises', async () => {
      jest.spyOn(exerciseDataService, 'loadAllExercises').mockResolvedValue([]);

      const result = await exerciseDataService.getAvailablePrimaryMuscles();

      expect(result).toEqual([]);
    });
  });

  describe('getAvailableEquipmentForMuscle', () => {
    it('returns available equipment for specific muscle', async () => {
      const exercises = [
        { ...mockExercise, muscleGroup: 'chest', equipment: 'bodyweight' },
        { ...mockExercise, id: 'bench-press', name: 'Bench Press', muscleGroup: 'chest', equipment: 'barbell' },
        { ...mockExercise, id: 'squats', name: 'Squats', muscleGroup: 'legs', equipment: 'bodyweight' },
      ];
      
      jest.spyOn(exerciseDataService, 'loadAllExercises').mockResolvedValue(exercises);

      const result = await exerciseDataService.getAvailableEquipmentForMuscle('chest');

      expect(result).toEqual(['bodyweight', 'barbell']);
    });

    it('returns empty array for non-existent muscle', async () => {
      jest.spyOn(exerciseDataService, 'loadAllExercises').mockResolvedValue([mockExercise]);

      const result = await exerciseDataService.getAvailableEquipmentForMuscle('nonexistent');

      expect(result).toEqual([]);
    });
  });

  describe('searchExercisesInMuscleGroup', () => {
    it('searches exercises by name in specific muscle group', async () => {
      const exercises = [
        { ...mockExercise, name: 'Push-ups', muscleGroup: 'chest' },
        { ...mockExercise, id: 'bench-press', name: 'Bench Press', muscleGroup: 'chest' },
        { ...mockExercise, id: 'squats', name: 'Squats', muscleGroup: 'legs' },
      ];
      
      jest.spyOn(exerciseDataService, 'loadAllExercises').mockResolvedValue(exercises);

      const result = await exerciseDataService.searchExercisesInMuscleGroup('chest', 'push');

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual(expect.objectContaining({
        name: 'Push-ups',
        muscleGroup: 'chest',
      }));
    });

    it('performs case-insensitive search', async () => {
      const exercises = [
        { ...mockExercise, name: 'Push-ups', muscleGroup: 'chest' },
      ];
      
      jest.spyOn(exerciseDataService, 'loadAllExercises').mockResolvedValue(exercises);

      const result = await exerciseDataService.searchExercisesInMuscleGroup('chest', 'PUSH');

      expect(result).toHaveLength(1);
      expect(result[0].name).toBe('Push-ups');
    });

    it('returns empty array when no matches found', async () => {
      const exercises = [
        { ...mockExercise, name: 'Push-ups', muscleGroup: 'chest' },
      ];
      
      jest.spyOn(exerciseDataService, 'loadAllExercises').mockResolvedValue(exercises);

      const result = await exerciseDataService.searchExercisesInMuscleGroup('chest', 'nonexistent');

      expect(result).toEqual([]);
    });

    it('returns empty array for non-existent muscle group', async () => {
      jest.spyOn(exerciseDataService, 'loadAllExercises').mockResolvedValue([mockExercise]);

      const result = await exerciseDataService.searchExercisesInMuscleGroup('nonexistent', 'push');

      expect(result).toEqual([]);
    });
  });

  describe('clearCache', () => {
    it('clears the exercise cache', async () => {
      // Mock loadAllExercises to track calls
      const loadAllExercisesSpy = jest.spyOn(exerciseDataService, 'loadAllExercises');
      
      // First call to populate cache
      loadAllExercisesSpy.mockResolvedValueOnce([mockExercise]);
      const result1 = await exerciseDataService.getExercisesGroupedByPrimaryMuscleAndEquipment();
      
      // Second call should use cache (loadAllExercises not called again)
      const result2 = await exerciseDataService.getExercisesGroupedByPrimaryMuscleAndEquipment();
      
      expect(loadAllExercisesSpy).toHaveBeenCalledTimes(1);
      expect(result1).toEqual(result2);
      
      // Clear cache
      exerciseDataService.clearCache();
      
      // Third call should reload data (loadAllExercises called again)
      loadAllExercisesSpy.mockResolvedValueOnce([mockExercise]);
      const result3 = await exerciseDataService.getExercisesGroupedByPrimaryMuscleAndEquipment();
      
      expect(loadAllExercisesSpy).toHaveBeenCalledTimes(2);
      expect(result3).toEqual(result1);
      
      loadAllExercisesSpy.mockRestore();
    });
  });
});