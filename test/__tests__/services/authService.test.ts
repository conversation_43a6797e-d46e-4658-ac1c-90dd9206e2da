import { authService } from '../../../src/services/authService';
import { supabase } from '../../../src/lib/supabase';
import type { User, Session } from '@supabase/supabase-js';

// Mock the supabase module
jest.mock('../../../src/lib/supabase', () => ({
  supabase: {
    auth: {
      signUp: jest.fn(),
      signInWithPassword: jest.fn(),
      signOut: jest.fn(),
      getUser: jest.fn(),
      onAuthStateChange: jest.fn(),
    },
  },
}));

const mockSupabase = supabase as any;

describe('authService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  describe('signUp', () => {
    it('should successfully sign up a user', async () => {
      const mockUser: User = {
        id: '123',
        email: '<EMAIL>',
        created_at: '2023-01-01T00:00:00Z',
        app_metadata: {},
        user_metadata: {},
        aud: 'authenticated',
        confirmation_sent_at: '2023-01-01T00:00:00Z',
      };

      const mockData = {
        user: mockUser,
        session: null,
      };

      mockSupabase.auth.signUp.mockResolvedValue({
        data: mockData,
        error: null,
      });

      const result = await authService.signUp('<EMAIL>', 'password123');

      expect(mockSupabase.auth.signUp).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password123',
      });
      expect(result).toEqual(mockData);
    });

    it('should throw error when sign up fails', async () => {
      const mockError = new Error('Sign up failed');
      mockSupabase.auth.signUp.mockResolvedValue({
        data: null,
        error: mockError,
      });

      await expect(authService.signUp('<EMAIL>', 'password123'))
        .rejects.toThrow('Sign up failed');

      expect(mockSupabase.auth.signUp).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password123',
      });
    });
  });

  describe('signIn', () => {
    it('should successfully sign in a user', async () => {
      const mockUser: User = {
        id: '123',
        email: '<EMAIL>',
        created_at: '2023-01-01T00:00:00Z',
        app_metadata: {},
        user_metadata: {},
        aud: 'authenticated',
        confirmation_sent_at: '2023-01-01T00:00:00Z',
      };

      const mockSession: Session = {
        access_token: 'mock-access-token',
        refresh_token: 'mock-refresh-token',
        expires_in: 3600,
        expires_at: 1234567890,
        token_type: 'bearer',
        user: mockUser,
      };

      const mockData = {
        user: mockUser,
        session: mockSession,
      };

      mockSupabase.auth.signInWithPassword.mockResolvedValue({
        data: mockData,
        error: null,
      });

      const result = await authService.signIn('<EMAIL>', 'password123');

      expect(mockSupabase.auth.signInWithPassword).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password123',
      });
      expect(result).toEqual(mockData);
    });

    it('should throw error when sign in fails', async () => {
      const mockError = new Error('Invalid credentials');
      mockSupabase.auth.signInWithPassword.mockResolvedValue({
        data: null,
        error: mockError,
      });

      await expect(authService.signIn('<EMAIL>', 'wrongpassword'))
        .rejects.toThrow('Invalid credentials');

      expect(mockSupabase.auth.signInWithPassword).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'wrongpassword',
      });
    });
  });

  describe('signOut', () => {
    it('should successfully sign out', async () => {
      mockSupabase.auth.signOut.mockResolvedValue({
        error: null,
      });

      await expect(authService.signOut()).resolves.not.toThrow();
      expect(mockSupabase.auth.signOut).toHaveBeenCalled();
    });

    it('should throw error when sign out fails', async () => {
      const mockError = new Error('Sign out failed');
      mockSupabase.auth.signOut.mockResolvedValue({
        error: mockError,
      });

      await expect(authService.signOut()).rejects.toThrow('Sign out failed');
      expect(mockSupabase.auth.signOut).toHaveBeenCalled();
    });
  });

  describe('getCurrentUser', () => {
    it('should return current user when authenticated', async () => {
      const mockUser: User = {
        id: '123',
        email: '<EMAIL>',
        created_at: '2023-01-01T00:00:00Z',
        app_metadata: {},
        user_metadata: {},
        aud: 'authenticated',
        confirmation_sent_at: '2023-01-01T00:00:00Z',
      };

      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      });

      const result = await authService.getCurrentUser();

      expect(mockSupabase.auth.getUser).toHaveBeenCalled();
      expect(result).toEqual(mockUser);
    });

    it('should return null when not authenticated', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: null,
      });

      const result = await authService.getCurrentUser();

      expect(mockSupabase.auth.getUser).toHaveBeenCalled();
      expect(result).toBeNull();
    });
  });

  describe('onAuthStateChange', () => {
    it('should set up auth state change listener', () => {
      const mockCallback = jest.fn();
      const mockUnsubscribe = jest.fn();
      
      mockSupabase.auth.onAuthStateChange.mockReturnValue({
        data: {
          subscription: {
            unsubscribe: mockUnsubscribe,
          },
        },
      });

      const result = authService.onAuthStateChange(mockCallback);

      expect(mockSupabase.auth.onAuthStateChange).toHaveBeenCalledWith(
        expect.any(Function)
      );
      expect(result).toEqual({
        data: {
          subscription: {
            unsubscribe: mockUnsubscribe,
          },
        },
      });
    });

    it('should call callback with user when session exists', () => {
      const mockCallback = jest.fn();
      const mockUser: User = {
        id: '123',
        email: '<EMAIL>',
        created_at: '2023-01-01T00:00:00Z',
        app_metadata: {},
        user_metadata: {},
        aud: 'authenticated',
        confirmation_sent_at: '2023-01-01T00:00:00Z',
      };

      const mockSession: Session = {
        access_token: 'mock-access-token',
        refresh_token: 'mock-refresh-token',
        expires_in: 3600,
        expires_at: 1234567890,
        token_type: 'bearer',
        user: mockUser,
      };

      let capturedCallback: (session: Session) => void;
      mockSupabase.auth.onAuthStateChange.mockImplementation((callback: any) => {
        capturedCallback = callback;
        return {
          data: {
            subscription: {
              unsubscribe: jest.fn(),
            },
          },
        };
      });

      authService.onAuthStateChange(mockCallback);

      // Simulate auth state change with session
      capturedCallback!(mockSession);

      expect(mockCallback).toHaveBeenCalledWith(mockUser);
    });

    it('should call callback with null when session is null', () => {
      const mockCallback = jest.fn();

      let capturedCallback: (session: Session | null) => void;
      mockSupabase.auth.onAuthStateChange.mockImplementation((callback: any) => {
        capturedCallback = callback;
        return {
          data: {
            subscription: {
              unsubscribe: jest.fn(),
            },
          },
        };
      });

      authService.onAuthStateChange(mockCallback);

      // Simulate auth state change with null session
      capturedCallback!(null);

      expect(mockCallback).toHaveBeenCalledWith(null);
    });
  });
});