import { exerciseService } from '../../../src/services/exerciseService';
import { supabase, DatabaseExercise } from '../../../src/lib/supabase';
import { Exercise, JsonExercise } from '../../../src/types';

// Mock dependencies
jest.mock('../../../src/lib/supabase', () => ({
  supabase: {
    auth: {
      getUser: jest.fn(),
    },
    from: jest.fn(),
  },
}));

// Mock fetch for GitHub exercises
global.fetch = jest.fn();

const mockSupabase = supabase as jest.Mocked<typeof supabase>;
const mockFetch = global.fetch as jest.MockedFunction<typeof fetch>;

describe('exerciseService', () => {
  const mockJsonExercise: JsonExercise = {
    id: 'push-ups',
    name: 'Push-ups',
    primary_muscles: ['chest'],
    secondary_muscles: ['triceps', 'shoulders'],
    instructions: ['Get into plank position', 'Lower body', 'Push up'],
    category: 'strength',
    equipment: 'bodyweight',
    level: 'beginner',
  };

  const mockExercise: Exercise = {
    id: 'push-ups',
    name: 'Push-ups',
    muscleGroup: 'chest',
    equipment: 'bodyweight',
    description: 'Classic push-up exercise',
    primaryMuscles: ['chest'],
    secondaryMuscles: ['triceps', 'shoulders'],
    instructions: ['Get into plank position', 'Lower body', 'Push up'],
    category: 'strength',
    level: 'beginner',
    mechanic: 'compound',
    force: 'push',
  };

  const mockUser = { id: 'user-123', email: '<EMAIL>' };

  beforeEach(() => {
    jest.clearAllMocks();
    mockSupabase.auth.getUser.mockResolvedValue({
      data: { user: mockUser },
      error: null,
    });
  });

  describe('fetchExercisesJson', () => {
    it('fetches exercises from URL successfully', async () => {
      const mockResponse = {
        ok: true,
        json: jest.fn().mockResolvedValue([mockJsonExercise]),
      };
      mockFetch.mockResolvedValue(mockResponse as any);

      const result = await exerciseService.fetchExercisesJson('/exercises.json');

      expect(result).toEqual([mockJsonExercise]);
      expect(mockFetch).toHaveBeenCalledWith('/exercises.json');
    });

    it('handles fetch errors gracefully', async () => {
      mockFetch.mockRejectedValue(new Error('Network error'));

      const result = await exerciseService.fetchExercisesJson('/exercises.json');

      expect(result).toEqual([]);
    });

    it('handles non-ok response', async () => {
      const mockResponse = {
        ok: false,
        status: 404,
      };
      mockFetch.mockResolvedValue(mockResponse as any);

      const result = await exerciseService.fetchExercisesJson('/exercises.json');

      expect(result).toEqual([]);
    });
  });

  describe('getExerciseSummaries', () => {
    it('converts single exercise to summary array', () => {
      const result = exerciseService.getExerciseSummaries(mockJsonExercise);

      expect(result).toEqual([{
        name: 'Push-ups',
        primary_muscles: ['chest'],
      }]);
    });

    it('converts exercise array to summaries', () => {
      const exercises = [mockJsonExercise, { ...mockJsonExercise, name: 'Squats', primary_muscles: ['legs'] }];
      const result = exerciseService.getExerciseSummaries(exercises);

      expect(result).toHaveLength(2);
      expect(result[0]).toEqual({
        name: 'Push-ups',
        primary_muscles: ['chest'],
      });
      expect(result[1]).toEqual({
        name: 'Squats',
        primary_muscles: ['legs'],
      });
    });
  });

  describe('mapGitHubToExercise', () => {
    it('maps GitHub exercise to Exercise type', () => {
      const result = exerciseService.mapGitHubToExercise(mockJsonExercise);

      expect(result).toEqual(expect.objectContaining({
        id: 'github-push-ups',
        name: 'Push-ups',
        muscleGroup: 'chest',
        equipment: 'bodyweight',
        description: 'Get into plank position Lower body Push up',
      }));
    });

    it('returns null for invalid exercise', () => {
      const invalidExercise = { ...mockJsonExercise, primary_muscles: [] };
      const result = exerciseService.mapGitHubToExercise(invalidExercise);

      expect(result).toBeNull();
    });

    it('returns null for exercise without name', () => {
      const invalidExercise = { ...mockJsonExercise, name: '' };
      const result = exerciseService.mapGitHubToExercise(invalidExercise);

      expect(result).toBeNull();
    });
  });

  describe('groupExercisesByPrimaryMuscle', () => {
    it('groups exercises correctly', () => {
      const exercises = [
        { ...mockExercise, muscleGroup: 'chest', primaryMuscles: ['chest'] },
        { ...mockExercise, id: 'squats', name: 'Squats', muscleGroup: 'legs', primaryMuscles: ['legs'] },
        { ...mockExercise, id: 'bench-press', name: 'Bench Press', muscleGroup: 'chest', primaryMuscles: ['chest'] },
      ];

      const result = exerciseService.groupExercisesByPrimaryMuscle(exercises);

      expect(result).toEqual({
        chest: [
          expect.objectContaining({ name: 'Push-ups', primaryMuscles: ['chest'] }),
          expect.objectContaining({ name: 'Bench Press', primaryMuscles: ['chest'] }),
        ],
        legs: [expect.objectContaining({ name: 'Squats', primaryMuscles: ['legs'] })],
      });
    });

    it('handles empty exercise list', () => {
      const result = exerciseService.groupExercisesByPrimaryMuscle([]);

      expect(result).toEqual({});
    });
  });

  describe('createCustomExercise', () => {
    it('creates a custom exercise successfully', async () => {
      const customExerciseData: Omit<Exercise, 'id'> = {
        name: 'Custom Push-ups',
        muscleGroup: 'chest',
        equipment: 'bodyweight',
        description: 'Custom push-up variation',
        secondaryMuscles: ['triceps'],
        instructions: ['Custom instruction'],
        category: 'strength',
        level: 'intermediate',
      };

      const mockInsert = jest.fn().mockReturnValue({
        select: jest.fn().mockReturnValue({
          single: jest.fn().mockResolvedValue({
            data: {
              id: 'custom-1',
              name: 'Custom Push-ups',
              muscle_group: 'chest',
              equipment: 'bodyweight',
              description: 'Custom push-up variation',
              created_at: '2024-01-01',
              created_by: 'user-123',
              is_default: false,
            },
            error: null,
          }),
        }),
      });

      mockSupabase.from.mockReturnValue({ insert: mockInsert } as any);

      const result = await exerciseService.createCustomExercise(customExerciseData);

      expect(result).toEqual(expect.objectContaining({
        id: 'custom-1',
        name: 'Custom Push-ups',
        muscleGroup: 'chest',
        equipment: 'bodyweight',
        description: 'Custom push-up variation',
      }));

      expect(mockSupabase.from).toHaveBeenCalledWith('exercises');
    });

    it('throws error when user is not authenticated', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: null,
      });

      const customExerciseData: Omit<Exercise, 'id'> = {
        name: 'Custom Exercise',
        muscleGroup: 'chest',
        equipment: 'bodyweight',
        description: 'Custom exercise',
      };

      await expect(
        exerciseService.createCustomExercise(customExerciseData)
      ).rejects.toThrow('User must be authenticated to create custom exercises');
    });
  });

  describe('mapDatabaseToExercise', () => {
    it('maps database record to exercise correctly', () => {
      const dbRecord: DatabaseExercise = {
        id: 'custom-1',
        name: 'Custom Exercise',
        muscle_group: 'chest',
        equipment: 'bodyweight',
        description: 'Custom exercise description',
        created_at: '2024-01-01',
        created_by: 'user-123',
        is_default: false,
      };

      const result = exerciseService.mapDatabaseToExercise(dbRecord);

      expect(result).toEqual({
        id: 'custom-1',
        name: 'Custom Exercise',
        muscleGroup: 'chest',
        equipment: 'bodyweight',
        description: 'Custom exercise description',
      });
    });
  });

  describe('getFallbackExercisesByMuscleGroup', () => {
    it('returns fallback exercises for chest', () => {
      const result = exerciseService.getFallbackExercisesByMuscleGroup('chest');

      expect(result).toHaveLength(4);
      expect(result[0]).toEqual(expect.objectContaining({
        name: 'Push-ups',
        muscleGroup: 'chest',
        equipment: 'bodyweight',
      }));
    });

    it('returns fallback exercises for legs', () => {
      const result = exerciseService.getFallbackExercisesByMuscleGroup('legs');

      expect(result).toHaveLength(4);
      expect(result[0]).toEqual(expect.objectContaining({
        name: 'Bodyweight Squats',
        muscleGroup: 'legs',
        equipment: 'bodyweight',
      }));
    });
  });

  describe('getFallbackExercises', () => {
    it('returns all fallback exercises', () => {
      const result = exerciseService.getFallbackExercises();

      expect(result.length).toBeGreaterThan(0);
      expect(result).toEqual(
        expect.arrayContaining([
          expect.objectContaining({ muscleGroup: 'chest' }),
          expect.objectContaining({ muscleGroup: 'back' }),
          expect.objectContaining({ muscleGroup: 'legs' }),
          expect.objectContaining({ muscleGroup: 'shoulders' }),
          expect.objectContaining({ muscleGroup: 'core' }),
          expect.objectContaining({ muscleGroup: 'glutes' }),
        ])
      );
    });
  });
});