import { workoutService } from '../../../src/services/workoutService';
import { supabase } from '../../../src/lib/supabase';
import { Workout, ExerciseSession } from '../../../src/types';

// Mock Supabase
jest.mock('../../../src/lib/supabase', () => ({
  supabase: {
    auth: {
      getUser: jest.fn(),
    },
    from: jest.fn(),
  },
}));

const mockSupabase = supabase as jest.Mocked<typeof supabase>;

describe('workoutService', () => {
  const mockUser = { id: 'user-123', email: '<EMAIL>' };
  const mockWorkoutData = {
    id: 'workout-1',
    date: '2024-01-15',
    start_time: '10:00',
    end_time: '11:00',
    muscle_group: 'chest',
    total_sets: 3,
    total_reps: 30,
  };

  const mockExerciseSession: ExerciseSession = {
    id: 'session-1',
    exerciseId: 'exercise-1',
    exerciseName: 'Push-ups',
    sets: [{ reps: 10, weight: 0 }],
    startTime: '10:00',
    endTime: '10:15',
    muscleGroup: 'chest',
    maxWeight: 0,
  };

  const mockWorkout: Omit<Workout, 'id'> = {
    date: '2024-01-15',
    startTime: '10:00',
    endTime: '11:00',
    muscleGroup: 'chest',
    exercises: [mockExerciseSession],
    totalSets: 3,
    totalReps: 30,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockSupabase.auth.getUser.mockResolvedValue({
      data: { user: mockUser },
      error: null,
    });
  });

  describe('saveWorkout', () => {
    it('saves a workout successfully', async () => {
      const mockInsert = jest.fn().mockReturnValue({
        select: jest.fn().mockReturnValue({
          single: jest.fn().mockResolvedValue({
            data: mockWorkoutData,
            error: null,
          }),
        }),
      });

      const mockSessionInsert = jest.fn().mockReturnValue({
        select: jest.fn().mockReturnValue({
          single: jest.fn().mockResolvedValue({
            data: {
              id: 'session-1',
              workout_id: 'workout-1',
              exercise_id: 'exercise-1',
              exercise_name: 'Push-ups',
              start_time: '10:00',
              end_time: '10:15',
              muscle_group: 'chest',
              max_weight: 0,
            },
            error: null,
          }),
        }),
      });

      const mockSetInsert = jest.fn().mockReturnValue({
        select: jest.fn().mockReturnValue({
          single: jest.fn().mockResolvedValue({
            data: { reps: 10, weight: 0 },
            error: null,
          }),
        }),
      });

      mockSupabase.from
        .mockReturnValueOnce({ insert: mockInsert } as any)
        .mockReturnValueOnce({ insert: mockSessionInsert } as any)
        .mockReturnValueOnce({ insert: mockSetInsert } as any);

      const result = await workoutService.saveWorkout(mockWorkout);

      expect(result).toEqual({
        id: 'workout-1',
        date: '2024-01-15',
        startTime: '10:00',
        endTime: '11:00',
        muscleGroup: 'chest',
        totalSets: 3,
        totalReps: 30,
        exercises: expect.any(Array),
      });

      expect(mockSupabase.from).toHaveBeenCalledWith('workouts');
      expect(mockInsert).toHaveBeenCalledWith({
        user_id: 'user-123',
        date: '2024-01-15',
        start_time: '10:00',
        end_time: '11:00',
        muscle_group: 'chest',
        total_sets: 3,
        total_reps: 30,
      });
    });

    it('throws error when user is not authenticated', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: null,
      });

      await expect(workoutService.saveWorkout(mockWorkout)).rejects.toThrow(
        'User must be authenticated to save workouts'
      );
    });

    it('handles database errors', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      });

      const mockInsert = jest.fn().mockReturnValue({
        select: jest.fn().mockReturnValue({
          single: jest.fn().mockResolvedValue({
            data: null,
            error: { message: 'Database error' },
          }),
        }),
      });

      mockSupabase.from.mockReturnValue({ insert: mockInsert } as any);

      try {
        await workoutService.saveWorkout(mockWorkout);
        fail('Expected function to throw');
      } catch (error) {
        expect(error).toBeDefined();
      }
    });
  });

  describe('getWorkouts', () => {
    it('fetches workouts for authenticated user', async () => {
      const mockSelect = jest.fn().mockReturnValue({
        eq: jest.fn().mockReturnValue({
          order: jest.fn().mockReturnValue({
            order: jest.fn().mockResolvedValue({
              data: [{
                ...mockWorkoutData,
                exercise_sessions: [{
                  id: 'session-1',
                  exercise_id: 'exercise-1',
                  exercise_name: 'Push-ups',
                  start_time: '10:00',
                  end_time: '10:15',
                  muscle_group: 'chest',
                  max_weight: 0,
                  workout_sets: [{ reps: 10, weight: 0 }],
                }],
              }],
              error: null,
            }),
          }),
        }),
      });

      mockSupabase.from.mockReturnValue({ select: mockSelect } as any);

      const result = await workoutService.getWorkouts();

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual(expect.objectContaining({
        id: 'workout-1',
        date: '2024-01-15',
        muscleGroup: 'chest',
      }));
    });

    it('returns empty array when user is not authenticated', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: null,
      });

      const result = await workoutService.getWorkouts();

      expect(result).toEqual([]);
    });
  });

  describe('getMonthlyAnalytics', () => {
    it('calculates monthly analytics correctly', async () => {
      const mockSelect = jest.fn().mockReturnValue({
        eq: jest.fn().mockReturnValue({
          gte: jest.fn().mockReturnValue({
            lte: jest.fn().mockResolvedValue({
              data: [{
                ...mockWorkoutData,
                exercise_sessions: [{
                  id: 'session-1',
                  exercise_id: 'exercise-1',
                  exercise_name: 'Push-ups',
                  start_time: '10:00',
                  end_time: '10:15',
                  muscle_group: 'chest',
                  max_weight: 50,
                  workout_sets: [{ reps: 10, weight: 50 }],
                }],
              }],
              error: null,
            }),
          }),
        }),
      });

      mockSupabase.from.mockReturnValue({ select: mockSelect } as any);

      const result = await workoutService.getMonthlyAnalytics(0, 2024);

      expect(result).toEqual({
        month: 'January',
        year: 2024,
        totalWorkouts: 1,
        totalSets: 3,
        totalReps: 30,
        muscleGroups: { chest: 1 },
        exerciseMaxWeights: { 'Push-ups': 50 },
        averageWorkoutDuration: expect.any(Number),
      });
    });

    it('returns empty analytics when user is not authenticated', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: null,
      });

      const result = await workoutService.getMonthlyAnalytics(0, 2024);

      expect(result).toEqual({
        month: 'January',
        year: 2024,
        totalWorkouts: 0,
        totalSets: 0,
        totalReps: 0,
        muscleGroups: {},
        exerciseMaxWeights: {},
        averageWorkoutDuration: 0,
      });
    });
  });

  describe('updateWorkout', () => {
    it('updates workout successfully', async () => {
      const mockUpdate = jest.fn().mockReturnValue({
        eq: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            select: jest.fn().mockReturnValue({
              single: jest.fn().mockResolvedValue({
                data: mockWorkoutData,
                error: null,
              }),
            }),
          }),
        }),
      });

      const mockSelect = jest.fn().mockReturnValue({
        eq: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: {
                ...mockWorkoutData,
                exercise_sessions: [],
              },
              error: null,
            }),
          }),
        }),
      });

      mockSupabase.from
        .mockReturnValueOnce({ update: mockUpdate } as any)
        .mockReturnValueOnce({ select: mockSelect } as any);

      const updates = { endTime: '11:30', totalSets: 4 };
      const result = await workoutService.updateWorkout('workout-1', updates);

      expect(result).toEqual(expect.objectContaining({
        id: 'workout-1',
        endTime: '11:00', // From the mocked data
      }));
    });
  });

  describe('deleteWorkout', () => {
    it('deletes workout successfully', async () => {
      const mockDelete = jest.fn().mockReturnValue({
        eq: jest.fn().mockReturnValue({
          eq: jest.fn().mockResolvedValue({
            error: null,
          }),
        }),
      });

      mockSupabase.from.mockReturnValue({ delete: mockDelete } as any);

      await workoutService.deleteWorkout('workout-1');

      expect(mockSupabase.from).toHaveBeenCalledWith('workouts');
      expect(mockDelete).toHaveBeenCalled();
    });

    it('throws error when user is not authenticated', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: null,
      });

      await expect(workoutService.deleteWorkout('workout-1')).rejects.toThrow(
        'User must be authenticated to delete workouts'
      );
    });
  });
});