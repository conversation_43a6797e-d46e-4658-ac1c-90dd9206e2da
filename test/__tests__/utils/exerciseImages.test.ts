import {
  getImageFromData,
  getExerciseImageById,
  getMuscleGroupImage,
  getFallbackImage,
  getExerciseSpecificFallback,
  getExerciseImage,
} from '../../../src/utils/exerciseImages';

// Mock console.log to avoid noise in tests
const mockConsoleLog = jest.spyOn(console, 'log').mockImplementation(() => {});

describe('exerciseImages utilities', () => {
  afterEach(() => {
    mockConsoleLog.mockClear();
  });

  afterAll(() => {
    mockConsoleLog.mockRestore();
  });

  describe('getImageFromData', () => {
    it('returns correct image path for exercise ID', () => {
      const result = getImageFromData('push-ups');
      expect(result).toBe('/exercises/push-ups/0.jpg');
    });

    it('logs the image loading', () => {
      getImageFromData('bench-press');
      expect(mockConsoleLog).toHaveBeenCalledWith(
        '🖼️ Loading image for exercise ID "bench-press": /exercises/bench-press/0.jpg'
      );
    });

    it('handles exercise IDs with special characters', () => {
      const result = getImageFromData('barbell-squat_variation');
      expect(result).toBe('/exercises/barbell-squat_variation/0.jpg');
    });
  });

  describe('getExerciseImageById', () => {
    it('returns correct image path for exercise ID', () => {
      const result = getExerciseImageById('deadlift');
      expect(result).toBe('/exercises/deadlift/0.jpg');
    });

    it('handles empty exercise ID', () => {
      const result = getExerciseImageById('');
      expect(result).toBe('/exercises//0.jpg');
    });
  });

  describe('getMuscleGroupImage', () => {
    it('returns correct image for known muscle groups', () => {
      expect(getMuscleGroupImage('legs')).toBe('/exercises/barbell_squat/0.jpg');
      expect(getMuscleGroupImage('glutes')).toBe('/exercises/barbell_hip_thrust/0.jpg');
      expect(getMuscleGroupImage('core')).toBe('/exercises/cable_crunch/0.jpg');
      expect(getMuscleGroupImage('back')).toBe('/exercises/lat_pull_down/0.jpg');
      expect(getMuscleGroupImage('shoulders')).toBe('/exercises/dumbbell_shoulder_press/0.jpg');
      expect(getMuscleGroupImage('chest')).toBe('/exercises/barbell_bench_press/0.jpg');
    });

    it('handles case insensitive muscle groups', () => {
      expect(getMuscleGroupImage('LEGS')).toBe('/exercises/barbell_squat/0.jpg');
      expect(getMuscleGroupImage('Chest')).toBe('/exercises/barbell_bench_press/0.jpg');
    });

    it('infers muscle group from exercise name keywords', () => {
      expect(getMuscleGroupImage('squat')).toBe('/exercises/barbell_squat/0.jpg');
      expect(getMuscleGroupImage('leg press')).toBe('/exercises/barbell_squat/0.jpg');
      expect(getMuscleGroupImage('hip thrust')).toBe('/exercises/barbell_hip_thrust/0.jpg');
      expect(getMuscleGroupImage('crunch')).toBe('/exercises/cable_crunch/0.jpg');
      expect(getMuscleGroupImage('plank')).toBe('/exercises/cable_crunch/0.jpg');
      expect(getMuscleGroupImage('row')).toBe('/exercises/lat_pull_down/0.jpg');
      expect(getMuscleGroupImage('pulldown')).toBe('/exercises/lat_pull_down/0.jpg');
      expect(getMuscleGroupImage('shoulder press')).toBe('/exercises/dumbbell_shoulder_press/0.jpg');
      expect(getMuscleGroupImage('lateral raise')).toBe('/exercises/dumbbell_shoulder_press/0.jpg');
      expect(getMuscleGroupImage('bench press')).toBe('/exercises/barbell_bench_press/0.jpg');
      expect(getMuscleGroupImage('push-up')).toBe('/exercises/barbell_bench_press/0.jpg');
    });

    it('defaults to chest for unknown exercise names', () => {
      expect(getMuscleGroupImage('unknown-exercise')).toBe('/exercises/barbell_bench_press/0.jpg');
      expect(getMuscleGroupImage('')).toBe('/exercises/barbell_bench_press/0.jpg');
    });

    it('handles complex exercise names with multiple keywords', () => {
      expect(getMuscleGroupImage('barbell squat')).toBe('/exercises/barbell_squat/0.jpg');
      expect(getMuscleGroupImage('dumbbell bench press')).toBe('/exercises/barbell_bench_press/0.jpg');
      expect(getMuscleGroupImage('cable lat pulldown')).toBe('/exercises/lat_pull_down/0.jpg');
    });
  });

  describe('getFallbackImage', () => {
    it('returns correct fallback images for muscle groups', () => {
      expect(getFallbackImage('legs')).toBe('/exercises/barbell_squat/0.jpg');
      expect(getFallbackImage('glutes')).toBe('/exercises/barbell_hip_thrust/0.jpg');
      expect(getFallbackImage('core')).toBe('/exercises/plank/0.jpg');
      expect(getFallbackImage('back')).toBe('/exercises/lat_pull_down/0.jpg');
      expect(getFallbackImage('shoulders')).toBe('/exercises/dumbbell_shoulder_press/0.jpg');
      expect(getFallbackImage('chest')).toBe('/exercises/barbell_bench_press/0.jpg');
    });

    it('defaults to chest for unknown muscle groups', () => {
      expect(getFallbackImage('unknown')).toBe('/exercises/barbell_bench_press/0.jpg');
      expect(getFallbackImage('')).toBe('/exercises/barbell_bench_press/0.jpg');
    });
  });

  describe('getExerciseSpecificFallback', () => {
    it('returns exercise-specific images based on keywords', () => {
      expect(getExerciseSpecificFallback('barbell-squat', 'legs', 'barbell'))
        .toBe('/exercises/Barbell_Squat/0.jpg');
      expect(getExerciseSpecificFallback('deadlift-variation', 'back', 'barbell'))
        .toBe('/exercises/Barbell_Deadlift/0.jpg');
      expect(getExerciseSpecificFallback('bench-press', 'chest', 'barbell'))
        .toBe('/exercises/Barbell_Bench_Press_-_Medium_Grip/0.jpg');
      expect(getExerciseSpecificFallback('barbell-row', 'back', 'barbell'))
        .toBe('/exercises/Bent_Over_Barbell_Row/0.jpg');
      expect(getExerciseSpecificFallback('bicep-curl', 'arms', 'barbell'))
        .toBe('/exercises/Barbell_Curl/0.jpg');
      expect(getExerciseSpecificFallback('lateral-raise', 'shoulders', 'dumbbell'))
        .toBe('/exercises/Dumbbell_Lateral_Raise/0.jpg');
      expect(getExerciseSpecificFallback('tricep-pushdown', 'arms', 'cable'))
        .toBe('/exercises/Triceps_Pushdown/0.jpg');
    });

    it('logs fallback decisions', () => {
      getExerciseSpecificFallback('squat-variation', 'legs', 'barbell');
      expect(mockConsoleLog).toHaveBeenCalledWith(
        '🔄 Fallback: Found keyword "squat" in "squat-variation", using Barbell_Squat'
      );
    });

    it('falls back to equipment-based images when no keywords match', () => {
      expect(getExerciseSpecificFallback('unknown-exercise', 'chest', 'dumbbell'))
        .toBe('/exercises/Dumbbell_Bicep_Curl/0.jpg');
      expect(getExerciseSpecificFallback('unknown-exercise', 'chest', 'barbell'))
        .toBe('/exercises/Barbell_Deadlift/0.jpg');
      expect(getExerciseSpecificFallback('unknown-exercise', 'chest', 'machine'))
        .toBe('/exercises/Wide-Grip_Lat_Pulldown/0.jpg');
      expect(getExerciseSpecificFallback('unknown-exercise', 'chest', 'bodyweight'))
        .toBe('/exercises/Push-Ups_-_Close_Triceps_Position/0.jpg');
    });

    it('logs equipment-based fallback decisions', () => {
      getExerciseSpecificFallback('unknown-exercise', 'chest', 'dumbbell');
      expect(mockConsoleLog).toHaveBeenCalledWith(
        '🔄 Fallback: Using equipment-based fallback for dumbbell'
      );
    });

    it('falls back to muscle group image for unknown equipment', () => {
      const result = getExerciseSpecificFallback('unknown-exercise', 'chest', 'unknown-equipment');
      expect(result).toBe('/exercises/barbell_bench_press/0.jpg'); // chest fallback
    });

    it('handles case insensitive exercise IDs', () => {
      expect(getExerciseSpecificFallback('SQUAT-variation', 'legs', 'barbell'))
        .toBe('/exercises/Barbell_Squat/0.jpg');
      expect(getExerciseSpecificFallback('DEADLIFT', 'back', 'barbell'))
        .toBe('/exercises/Barbell_Deadlift/0.jpg');
    });

    it('prioritizes first matching keyword', () => {
      // Exercise with multiple keywords should match the first one found
      expect(getExerciseSpecificFallback('squat-press-combo', 'legs', 'barbell'))
        .toBe('/exercises/Barbell_Squat/0.jpg'); // squat comes before press in the map
    });
  });

  describe('getExerciseImage', () => {
    it('is an alias for getImageFromData', () => {
      const exerciseId = 'test-exercise';
      const result1 = getExerciseImage(exerciseId);
      const result2 = getImageFromData(exerciseId);
      
      expect(result1).toBe(result2);
      expect(result1).toBe('/exercises/test-exercise/0.jpg');
    });
  });

  describe('edge cases', () => {
    it('handles null and undefined inputs gracefully', () => {
      expect(getImageFromData(null as any)).toBe('/exercises/null/0.jpg');
      expect(getImageFromData(undefined as any)).toBe('/exercises/undefined/0.jpg');
      expect(getMuscleGroupImage(null as any)).toBe('/exercises/barbell_bench_press/0.jpg');
      expect(getMuscleGroupImage(undefined as any)).toBe('/exercises/barbell_bench_press/0.jpg');
    });

    it('handles whitespace in inputs', () => {
      expect(getImageFromData('  push-ups  ')).toBe('/exercises/  push-ups  /0.jpg');
      expect(getMuscleGroupImage('  chest  ')).toBe('/exercises/barbell_bench_press/0.jpg');
    });

    it('handles special characters in exercise IDs', () => {
      const specialId = 'exercise-with-@#$%^&*()_+=';
      expect(getImageFromData(specialId)).toBe(`/exercises/${specialId}/0.jpg`);
    });
  });
});