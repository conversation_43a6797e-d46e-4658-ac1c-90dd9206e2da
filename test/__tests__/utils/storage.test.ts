import { saveWorkout, getWorkouts, deleteWorkout, getMonthlyAnalytics } from '../../../src/utils/storage';
import { Workout, MonthlyAnalytics } from '../../../src/types';

// Mock localStorage
const localStorageMock = (() => {
  let store: Record<string, string> = {};

  return {
    getItem: (key: string) => store[key] || null,
    setItem: (key: string, value: string) => {
      store[key] = value;
    },
    removeItem: (key: string) => {
      delete store[key];
    },
    clear: () => {
      store = {};
    },
  };
})();

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

describe('storage utilities', () => {
  const mockWorkout: Workout = {
    id: 'workout-1',
    date: '2024-01-15',
    startTime: '10:00',
    endTime: '11:30',
    muscleGroup: 'chest',
    totalSets: 12,
    totalReps: 120,
    exercises: [
      {
        id: 'session-1',
        exerciseId: 'push-ups',
        exerciseName: 'Push-ups',
        sets: [
          { reps: 10, weight: 0, duration: 0 },
          { reps: 8, weight: 0, duration: 0 },
        ],
        startTime: '10:00',
        endTime: '10:15',
        muscleGroup: 'chest',
        maxWeight: 0,
      },
      {
        id: 'session-2',
        exerciseId: 'bench-press',
        exerciseName: 'Bench Press',
        sets: [
          { reps: 10, weight: 135, duration: 0 },
          { reps: 8, weight: 155, duration: 0 },
        ],
        startTime: '10:15',
        endTime: '10:30',
        muscleGroup: 'chest',
        maxWeight: 155,
      },
    ],
  };

  const mockWorkout2: Workout = {
    id: 'workout-2',
    date: '2024-01-20',
    startTime: '14:00',
    endTime: '15:00',
    muscleGroup: 'legs',
    totalSets: 8,
    totalReps: 80,
    exercises: [
      {
        id: 'session-3',
        exerciseId: 'squats',
        exerciseName: 'Squats',
        sets: [
          { reps: 10, weight: 185, duration: 0 },
          { reps: 8, weight: 205, duration: 0 },
        ],
        startTime: '14:00',
        endTime: '14:30',
        muscleGroup: 'legs',
        maxWeight: 205,
      },
    ],
  };

  beforeEach(() => {
    localStorage.clear();
  });

  describe('saveWorkout', () => {
    it('saves a workout to localStorage', () => {
      saveWorkout(mockWorkout);

      const stored = localStorage.getItem('bodybuilder-workouts');
      expect(stored).toBeTruthy();
      
      const workouts = JSON.parse(stored!);
      expect(workouts).toHaveLength(1);
      expect(workouts[0]).toEqual(mockWorkout);
    });

    it('appends to existing workouts', () => {
      saveWorkout(mockWorkout);
      saveWorkout(mockWorkout2);

      const stored = localStorage.getItem('bodybuilder-workouts');
      const workouts = JSON.parse(stored!);
      
      expect(workouts).toHaveLength(2);
      expect(workouts[0]).toEqual(mockWorkout);
      expect(workouts[1]).toEqual(mockWorkout2);
    });
  });

  describe('getWorkouts', () => {
    it('returns empty array when no workouts stored', () => {
      const workouts = getWorkouts();
      expect(workouts).toEqual([]);
    });

    it('returns stored workouts', () => {
      localStorage.setItem('bodybuilder-workouts', JSON.stringify([mockWorkout, mockWorkout2]));

      const workouts = getWorkouts();
      expect(workouts).toHaveLength(2);
      expect(workouts[0]).toEqual(mockWorkout);
      expect(workouts[1]).toEqual(mockWorkout2);
    });

    it('handles invalid JSON gracefully', () => {
      localStorage.setItem('bodybuilder-workouts', 'invalid-json');

      expect(() => getWorkouts()).toThrow();
    });
  });

  describe('deleteWorkout', () => {
    beforeEach(() => {
      localStorage.setItem('bodybuilder-workouts', JSON.stringify([mockWorkout, mockWorkout2]));
    });

    it('deletes a workout by ID', () => {
      deleteWorkout('workout-1');

      const workouts = getWorkouts();
      expect(workouts).toHaveLength(1);
      expect(workouts[0].id).toBe('workout-2');
    });

    it('does nothing if workout ID not found', () => {
      deleteWorkout('non-existent');

      const workouts = getWorkouts();
      expect(workouts).toHaveLength(2);
    });

    it('handles empty workout list', () => {
      localStorage.clear();
      
      expect(() => deleteWorkout('workout-1')).not.toThrow();
      
      const workouts = getWorkouts();
      expect(workouts).toEqual([]);
    });
  });

  describe('getMonthlyAnalytics', () => {
    beforeEach(() => {
      // Create workouts for January 2024
      const januaryWorkouts = [
        {
          ...mockWorkout,
          id: 'jan-1',
          date: '2024-01-15',
          muscleGroup: 'chest',
          totalSets: 12,
          totalReps: 120,
        },
        {
          ...mockWorkout2,
          id: 'jan-2',
          date: '2024-01-20',
          muscleGroup: 'legs',
          totalSets: 8,
          totalReps: 80,
        },
        {
          ...mockWorkout,
          id: 'jan-3',
          date: '2024-01-25',
          muscleGroup: 'chest',
          totalSets: 10,
          totalReps: 100,
        },
      ];

      // Add a workout from different month
      const februaryWorkout = {
        ...mockWorkout,
        id: 'feb-1',
        date: '2024-02-10',
        muscleGroup: 'back',
        totalSets: 6,
        totalReps: 60,
      };

      localStorage.setItem('bodybuilder-workouts', JSON.stringify([...januaryWorkouts, februaryWorkout]));
    });

    it('calculates analytics for specific month and year', () => {
      const analytics = getMonthlyAnalytics(0, 2024); // January 2024

      expect(analytics.month).toBe('January');
      expect(analytics.year).toBe(2024);
      expect(analytics.totalWorkouts).toBe(3);
      expect(analytics.totalSets).toBe(30); // 12 + 8 + 10
      expect(analytics.totalReps).toBe(300); // 120 + 80 + 100
    });

    it('groups workouts by muscle group', () => {
      const analytics = getMonthlyAnalytics(0, 2024);

      expect(analytics.muscleGroups).toEqual({
        chest: 2,
        legs: 1,
      });
    });

    it('tracks exercise max weights', () => {
      const analytics = getMonthlyAnalytics(0, 2024);

      expect(analytics.exerciseMaxWeights).toEqual({
        'Push-ups': 0,
        'Bench Press': 155,
        'Squats': 205,
      });
    });

    it('calculates average workout duration', () => {
      const analytics = getMonthlyAnalytics(0, 2024);

      // Workouts: 90 min (10:00-11:30), 60 min (14:00-15:00), 90 min (10:00-11:30)
      // Average: (90 + 60 + 90) / 3 = 80 minutes
      expect(analytics.averageWorkoutDuration).toBe(80);
    });

    it('returns empty analytics for month with no workouts', () => {
      const analytics = getMonthlyAnalytics(5, 2024); // June 2024

      expect(analytics.month).toBe('June');
      expect(analytics.year).toBe(2024);
      expect(analytics.totalWorkouts).toBe(0);
      expect(analytics.totalSets).toBe(0);
      expect(analytics.totalReps).toBe(0);
      expect(analytics.muscleGroups).toEqual({});
      expect(analytics.exerciseMaxWeights).toEqual({});
      expect(analytics.averageWorkoutDuration).toBe(0);
    });

    it('filters workouts correctly by month and year', () => {
      const januaryAnalytics = getMonthlyAnalytics(0, 2024);
      const februaryAnalytics = getMonthlyAnalytics(1, 2024);

      expect(januaryAnalytics.totalWorkouts).toBe(3);
      expect(februaryAnalytics.totalWorkouts).toBe(1);
      expect(februaryAnalytics.muscleGroups).toEqual({ back: 1 });
    });

    it('handles workouts with different time formats', () => {
      const workoutWithDifferentTime = {
        ...mockWorkout,
        id: 'time-test',
        date: '2024-01-30',
        startTime: '09:15',
        endTime: '10:45',
        muscleGroup: 'arms',
        totalSets: 5,
        totalReps: 50,
        exercises: [],
      };

      const existingWorkouts = getWorkouts();
      localStorage.setItem('bodybuilder-workouts', JSON.stringify([...existingWorkouts, workoutWithDifferentTime]));

      const analytics = getMonthlyAnalytics(0, 2024);
      
      expect(analytics.totalWorkouts).toBe(4);
      expect(analytics.muscleGroups.arms).toBe(1);
    });
  });
});