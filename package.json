{"name": "vite-react-typescript-starter", "jest": {"preset": "ts-jest", "testEnvironment": "jsdom", "setupFilesAfterEnv": ["<rootDir>/test/setup.ts"], "clearMocks": true, "verbose": true, "moduleNameMapping": {"^@/(.*)$": "<rootDir>/src/$1"}, "transform": {"^.+\\.(ts|tsx)$": ["ts-jest", {"tsconfig": {"jsx": "react-jsx", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "moduleResolution": "node"}}]}, "moduleFileExtensions": ["ts", "tsx", "js", "jsx", "json"]}, "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "test": "jest"}, "dependencies": {"@capacitor/cli": "^7.4.2", "@capacitor/core": "^7.4.2", "@capacitor/ios": "^7.4.2", "@supabase/supabase-js": "^2.50.3", "@testing-library/react": "^16.3.0", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1"}, "devDependencies": {"@eslint/js": "^9.9.1", "@testing-library/jest-dom": "^6.4.2", "@types/jest": "^30.0.0", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "ts-jest": "^29.4.1", "ts-node": "^10.9.2", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^7.0.6"}}